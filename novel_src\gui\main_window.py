"""
main_window.py - GUI主窗口模块
"""
import os
import time
import threading
import requests
import ctypes
import re
from urllib.parse import urlparse, parse_qs
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import sys

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLineEdit, QLabel, QComboBox,
    QTextEdit, QFileDialog, QMessageBox, QTabWidget,
    QFrame, QGridLayout, QCheckBox, QSpinBox, QGroupBox,
    QProgressBar, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, pyqtSlot, QSize, QMetaObject, Q_ARG, QTimer
from PyQt5.QtGui import Q<PERSON>ixmap, <PERSON><PERSON>ont, QIcon

from ..base_system.activation import ActivationManager
from ..base_system.updater import Updater
from ..constants import VERSION
from ..network_parser.network import NetworkClient
from ..book_parser.book_manager import BookManager
from ..network_parser.downloader import ChapterDownloader
from ..book_parser.parser import ContentParser
from .update_window import UpdateWindow
from ..network_parser.qimao_downloader import QimaoDownloadWorker
from ..network_parser.download_task_manager import DownloadTaskManager, DownloadTaskStatus
from ..constants import API_BASE_URL


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 在初始化方法中导入GlobalContext，避免循环导入
        from ..base_system.context import GlobalContext

        # 获取全局配置和日志
        self.config = GlobalContext.get_config()
        self.logger = GlobalContext.get_logger()

        # 初始化网络客户端
        self.network = GlobalContext.get_network_client()

        # 初始化激活管理器
        self.activation_manager = ActivationManager()

        # 初始化更新器
        self.updater = Updater()

        # 初始化下载任务管理器
        self.task_manager = DownloadTaskManager()

        # 初始化任务状态查询定时器
        self.task_timer = None
        self.current_novel_id = None

        # 标记是否需要将TXT转换为EPUB
        self.need_convert_to_epub = False

        # 设置窗口标题和大小
        self.setWindowTitle(f"Useful Novel Tool v{VERSION}")
        self.resize(1000, 700)

        # 设置窗口图标和任务栏图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "img", "log_ico.ico")
        if os.path.exists(icon_path):
            app_icon = QIcon(icon_path)
            self.setWindowIcon(app_icon)

            # 在Windows系统上设置任务栏图标
            if os.name == 'nt':
                try:
                    # 获取应用程序ID
                    app_id = f"useful_novel_tool.app.v{VERSION}"
                    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
                    self.logger.info(f"已设置Windows任务栏应用程序ID: {app_id}")
                except Exception as e:
                    self.logger.warning(f"设置Windows任务栏图标时出错: {str(e)}")

            self.logger.info(f"已设置窗口图标和任务栏图标: {icon_path}")
        else:
            self.logger.warning(f"窗口图标文件不存在: {icon_path}")

        # 初始化UI
        self.init_ui()

        # 静默检查更新
        self.check_update_silently()

    def init_ui(self):
        """初始化UI组件"""
        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 8px;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            QComboBox {
                border: 2px solid #ddd;
                border-radius: 6px;
                padding: 8px;
                background-color: white;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
            }
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 6px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 4px;
            }
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 6px;
                background-color: white;
                padding: 8px;
            }
            QLabel {
                color: #333333;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建选项卡组件
        self.tab_widget = QTabWidget()

        # 添加下载选项卡
        download_tab = QWidget()
        self.setup_download_tab(download_tab)
        self.tab_widget.addTab(download_tab, "某茄小说")

        # 添加某猫小说选项卡
        qimao_tab = QWidget()
        self.setup_qimao_tab(qimao_tab)
        self.tab_widget.addTab(qimao_tab, "某猫小说")

        # 添加设置选项卡
        config_tab = QWidget()
        self.setup_config_tab(config_tab)
        self.tab_widget.addTab(config_tab, "软件设置")

        # 添加赞助选项卡
        sponsor_tab = QWidget()
        self.setup_sponsor_tab(sponsor_tab)
        self.tab_widget.addTab(sponsor_tab, "赞助支持")

        # 添加关于选项卡
        about_tab = QWidget()
        self.setup_about_tab(about_tab)
        self.tab_widget.addTab(about_tab, "关于软件")

        # 将选项卡组件添加到主布局
        main_layout.addWidget(self.tab_widget)

        # 添加状态栏
        self.status_label = QLabel()
        self.status_label.setText(f"准备就绪 | 版本: v{VERSION}")
        self.statusBar().addWidget(self.status_label)

        # 添加激活状态标签
        self.activation_status = QLabel()
        token = self.activation_manager.load_saved_token()
        if token:
            # 显示部分激活码，保护隐私
            masked_token = f"{token[:4]}...{token[-4:]}" if len(token) > 8 else token
            self.activation_status.setText(f"激活码: {masked_token} | ")
            self.logger.info("主窗口初始化完成，显示激活状态")
        else:
            self.activation_status.setText("未激活 | ")
            self.logger.warning("主窗口初始化完成，但未检测到有效激活状态")
        self.statusBar().addPermanentWidget(self.activation_status)

        # 默认选择下载选项卡
        self.tab_widget.setCurrentIndex(0)

    def setup_download_tab(self, tab):
        """设置下载标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 输入区域
        input_group = QGroupBox("📚 小说信息")
        input_layout = QGridLayout(input_group)
        input_layout.setSpacing(10)

        # 小说ID/链接输入
        input_layout.addWidget(QLabel("🔗 小说ID/链接:"), 0, 0)
        self.novel_id_input = QLineEdit()
        self.novel_id_input.setPlaceholderText("输入小说ID或分享链接，例如：7382159 或 https://fanqienovel.com/page/7382159")
        self.novel_id_input.setMinimumHeight(35)
        input_layout.addWidget(self.novel_id_input, 0, 1, 1, 2)

        # 保存路径
        input_layout.addWidget(QLabel("📁 保存路径:"), 1, 0)
        self.save_path_input = QLineEdit()
        self.save_path_input.setText(str(self.config.default_save_dir))
        self.save_path_input.setMinimumHeight(35)
        input_layout.addWidget(self.save_path_input, 1, 1)

        save_path_button = QPushButton("📂 浏览...")
        save_path_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        save_path_button.clicked.connect(self.browse_save_path)
        input_layout.addWidget(save_path_button, 1, 2)

        # 保存格式
        input_layout.addWidget(QLabel("📄 保存格式:"), 2, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["TXT 文本格式", "EPUB 电子书格式"])
        self.format_combo.setMinimumHeight(35)
        # 设置默认选项
        format_map = {"txt": 0, "epub": 1}
        index = format_map.get(self.config.novel_format, 0)
        self.format_combo.setCurrentIndex(index)
        input_layout.addWidget(self.format_combo, 2, 1, 1, 2)

        # 章节校对选项
        input_layout.addWidget(QLabel("🔧 章节校对:"), 3, 0)
        self.chapter_proofread_checkbox = QCheckBox("启用章节顺序校对")
        self.chapter_proofread_checkbox.setChecked(False)  # 默认不启用
        self.chapter_proofread_checkbox.setToolTip("下载完成后根据章节标题列表校对TXT文件中的章节顺序，解决章节混乱问题")
        self.chapter_proofread_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 3px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggM0w0IDdMMiA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
        input_layout.addWidget(self.chapter_proofread_checkbox, 3, 1, 1, 2)

        layout.addWidget(input_group)

        # 书籍信息显示区域
        book_info_group = QGroupBox("📖 书籍详情")
        book_info_layout = QHBoxLayout(book_info_group)
        book_info_layout.setSpacing(15)

        # 封面预览
        self.cover_label = QLabel()
        self.cover_label.setFixedSize(150, 200)
        self.cover_label.setAlignment(Qt.AlignCenter)
        self.cover_label.setText("📷\n暂无封面")
        self.cover_label.setStyleSheet("""
            border: 2px dashed #ccc;
            border-radius: 8px;
            background-color: #f9f9f9;
            color: #666;
            font-size: 14px;
        """)
        book_info_layout.addWidget(self.cover_label)

        # 书籍信息
        self.book_info_text = QTextEdit()
        self.book_info_text.setReadOnly(True)
        self.book_info_text.setPlaceholderText("获取书籍信息后显示详情...")
        book_info_layout.addWidget(self.book_info_text)

        layout.addWidget(book_info_group)

        # 下载进度区域
        progress_group = QGroupBox("⏬ 下载进度")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(10)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 8px;
                text-align: center;
                background-color: #f0f0f0;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #4CAF50, stop: 1 #45a049);
                border-radius: 6px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("📋 准备下载...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #555;
                font-size: 14px;
                padding: 5px;
            }
        """)
        progress_layout.addWidget(self.status_label)

        layout.addWidget(progress_group)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.download_button = QPushButton("🚀 开始下载")
        self.download_button.setMinimumHeight(40)
        self.download_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.download_button.clicked.connect(self.start_download)
        button_layout.addWidget(self.download_button)

        self.cancel_button = QPushButton("⏹️ 取消下载")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.cancel_button.clicked.connect(self.cancel_download)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)

        self.clear_button = QPushButton("🗑️ 清空信息")
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.clear_button.clicked.connect(self.clear_download_info)
        button_layout.addWidget(self.clear_button)

        layout.addLayout(button_layout)

    def clear_download_info(self):
        """清空下载信息"""
        self.novel_id_input.clear()
        self.book_info_text.clear()
        self.cover_label.setText("📷\n暂无封面")
        self.cover_label.setPixmap(QPixmap())  # 清空图片
        self.progress_bar.setValue(0)
        self.status_label.setText("📋 准备下载...")
        self.logger.info("已清空下载信息")

    def cancel_download(self):
        """取消下载"""
        if self.current_novel_id:
            # 取消当前任务
            self.task_manager.cancel_task(self.current_novel_id)
            self.current_novel_id = None

        # 停止定时器
        if self.task_timer:
            self.task_timer.stop()
            self.task_timer.deleteLater()
            self.task_timer = None

        # 恢复按钮状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.status_label.setText("📋 下载已取消")
        self.logger.info("用户取消下载")

    def setup_qimao_tab(self, tab):
        """设置某猫小说下载标签页"""
        # 创建主布局
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # ===== 输入区域 =====
        input_group = QGroupBox("🐱 某猫小说输入")
        input_layout = QVBoxLayout(input_group)
        input_layout.setSpacing(10)
        layout.addWidget(input_group)

        # 添加模式选择
        mode_layout = QHBoxLayout()
        mode_label = QLabel("获取方式:")
        self.qimao_id_radio = QRadioButton("输入ID/链接")
        self.qimao_search_radio = QRadioButton("搜索小说")
        self.qimao_id_radio.setChecked(True)

        mode_group = QButtonGroup()
        mode_group.addButton(self.qimao_id_radio)
        mode_group.addButton(self.qimao_search_radio)

        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.qimao_id_radio)
        mode_layout.addWidget(self.qimao_search_radio)
        mode_layout.addStretch(1)
        input_layout.addLayout(mode_layout)

        # ID/链接输入区域
        id_layout = QHBoxLayout()
        id_label = QLabel("小说ID或链接:")
        self.qimao_id_input = QLineEdit()
        self.qimao_id_input.setPlaceholderText("输入某猫小说ID或网址链接 (例如: 7382159)")
        id_layout.addWidget(id_label)
        id_layout.addWidget(self.qimao_id_input)
        input_layout.addLayout(id_layout)

        # 搜索输入区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索关键词:")
        self.qimao_search_input = QLineEdit()
        self.qimao_search_input.setPlaceholderText("输入要搜索的小说名称或作者")
        self.qimao_search_btn = QPushButton("搜索")
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.qimao_search_input)
        search_layout.addWidget(self.qimao_search_btn)
        input_layout.addLayout(search_layout)

        # 搜索结果显示区域
        self.qimao_search_result = QTableWidget(0, 4)  # 显示序号、书名、作者、状态
        self.qimao_search_result.setHorizontalHeaderLabels(["书名", "作者", "字数", "状态"])
        self.qimao_search_result.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.qimao_search_result.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.qimao_search_result.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.qimao_search_result.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.qimao_search_result.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.qimao_search_result.setSelectionMode(QAbstractItemView.SingleSelection)
        self.qimao_search_result.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.qimao_search_result.setMaximumHeight(200)
        input_layout.addWidget(self.qimao_search_result)

        # 下载路径选择
        path_layout = QHBoxLayout()
        path_label = QLabel("保存路径:")
        self.qimao_save_path = QLineEdit()

        # 使用配置中的某猫保存路径，如果不存在则使用默认路径
        qimao_save_path = getattr(self.config, 'qimao_save_dir', '')
        if not qimao_save_path:
            qimao_save_path = os.path.join(os.path.expanduser("~"), "Downloads", "某猫小说")

        self.qimao_save_path.setText(qimao_save_path)
        browse_button = QPushButton("浏览")
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.qimao_save_path)
        path_layout.addWidget(browse_button)
        input_layout.addLayout(path_layout)

        # 保存格式选择
        format_layout = QHBoxLayout()
        format_label = QLabel("保存格式:")
        self.qimao_format_combo = QComboBox()
        self.qimao_format_combo.addItems(["TXT 文本格式", "EPUB 电子书格式"])
        self.qimao_format_combo.setMinimumHeight(35)
        # 设置默认选项
        format_map = {"txt": 0, "epub": 1}
        index = format_map.get(self.config.novel_format, 0)
        self.qimao_format_combo.setCurrentIndex(index)
        format_layout.addWidget(format_label)
        format_layout.addWidget(self.qimao_format_combo)
        format_layout.addStretch(1)
        input_layout.addLayout(format_layout)

        # 下载按钮
        btn_layout = QHBoxLayout()
        self.qimao_download_btn = QPushButton("开始下载")
        self.qimao_cancel_btn = QPushButton("取消下载")
        self.qimao_cancel_btn.setEnabled(False)
        self.qimao_clear_btn = QPushButton("清空信息")
        btn_layout.addWidget(self.qimao_download_btn)
        btn_layout.addWidget(self.qimao_cancel_btn)
        btn_layout.addWidget(self.qimao_clear_btn)
        input_layout.addLayout(btn_layout)

        # ===== 下载信息区域 =====
        info_group = QGroupBox("下载信息")
        info_layout = QVBoxLayout(info_group)
        layout.addWidget(info_group)

        # 下载状态
        status_layout = QHBoxLayout()
        status_label = QLabel("状态:")
        self.qimao_status_text = QLabel("空闲")
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.qimao_status_text)
        status_layout.addStretch(1)
        info_layout.addLayout(status_layout)

        # 进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.qimao_progress_bar = QProgressBar()
        self.qimao_progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.qimao_progress_bar)
        info_layout.addLayout(progress_layout)

        # 书籍信息
        book_layout = QGridLayout()
        book_layout.addWidget(QLabel("书名:"), 0, 0)
        book_layout.addWidget(QLabel("作者:"), 1, 0)
        book_layout.addWidget(QLabel("字数:"), 2, 0)
        book_layout.addWidget(QLabel("标签:"), 3, 0)

        self.qimao_book_name = QLabel("--")
        self.qimao_book_author = QLabel("--")
        self.qimao_book_words = QLabel("--")
        self.qimao_book_tags = QLabel("--")

        book_layout.addWidget(self.qimao_book_name, 0, 1)
        book_layout.addWidget(self.qimao_book_author, 1, 1)
        book_layout.addWidget(self.qimao_book_words, 2, 1)
        book_layout.addWidget(self.qimao_book_tags, 3, 1)

        info_layout.addLayout(book_layout)

        # 简介
        info_layout.addWidget(QLabel("简介:"))
        self.qimao_book_intro = QTextEdit()
        self.qimao_book_intro.setReadOnly(True)
        self.qimao_book_intro.setMaximumHeight(80)
        info_layout.addWidget(self.qimao_book_intro)

        # 下载信息
        result_layout = QHBoxLayout()
        result_layout.addWidget(QLabel("下载信息:"))
        self.qimao_result_info = QLabel("--")
        result_layout.addWidget(self.qimao_result_info)
        result_layout.addStretch(1)
        info_layout.addLayout(result_layout)

        # 连接信号
        browse_button.clicked.connect(self.browse_qimao_save_path)
        self.qimao_download_btn.clicked.connect(self.start_qimao_download)
        self.qimao_cancel_btn.clicked.connect(self.cancel_qimao_download)
        self.qimao_clear_btn.clicked.connect(self.clear_qimao_info)
        self.qimao_search_btn.clicked.connect(self.search_qimao_novel)
        self.qimao_search_result.cellDoubleClicked.connect(self.select_qimao_search_result)

        # 设置初始状态
        self.qimao_search_result.hide()  # 初始隐藏搜索结果

        # 连接单选按钮信号
        self.qimao_id_radio.toggled.connect(self.toggle_qimao_input_mode)
        self.qimao_search_radio.toggled.connect(self.toggle_qimao_input_mode)

        # 初始设置
        self.toggle_qimao_input_mode()

    def setup_config_tab(self, tab):
        """设置配置选项卡"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 保存路径组
        path_group = QGroupBox("💾 保存设置")
        path_layout = QGridLayout()
        path_layout.setSpacing(10)

        # 通用保存路径
        path_layout.addWidget(QLabel("🍅 某茄小说保存路径:"), 0, 0)
        self.config_save_path_input = QLineEdit()
        self.config_save_path_input.setText(str(self.config.default_save_dir))
        self.config_save_path_input.setMinimumHeight(35)
        path_layout.addWidget(self.config_save_path_input, 0, 1)

        browse_button = QPushButton("📂 浏览...")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                min-width: 100px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        browse_button.clicked.connect(self.browse_config_save_path)
        path_layout.addWidget(browse_button, 0, 2)

        # 某猫小说保存路径
        path_layout.addWidget(QLabel("🐱 某猫小说保存路径:"), 1, 0)
        self.config_qimao_save_path_input = QLineEdit()
        # 获取配置的某猫保存路径，如果不存在则使用默认路径
        qimao_save_path = getattr(self.config, 'qimao_save_dir',
                                 os.path.join(os.path.expanduser("~"), "Downloads", "某猫小说"))
        self.config_qimao_save_path_input.setText(qimao_save_path)
        self.config_qimao_save_path_input.setMinimumHeight(35)
        path_layout.addWidget(self.config_qimao_save_path_input, 1, 1)

        qimao_browse_button = QPushButton("📂 浏览...")
        qimao_browse_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                min-width: 100px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        qimao_browse_button.clicked.connect(self.browse_qimao_config_save_path)
        path_layout.addWidget(qimao_browse_button, 1, 2)

        path_group.setLayout(path_layout)
        layout.addWidget(path_group)

        # 更新设置组
        update_group = QGroupBox("🔄 更新设置")
        update_layout = QGridLayout()
        update_layout.setSpacing(10)

        # 自动检查更新
        self.auto_check_update = QCheckBox("✅ 自动检查更新")
        self.auto_check_update.setChecked(self.config.auto_check_update)
        self.auto_check_update.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        update_layout.addWidget(self.auto_check_update, 0, 0, 1, 2)

        # 检查间隔
        update_layout.addWidget(QLabel("⏰ 检查间隔(小时):"), 1, 0)
        self.update_interval_spin = QSpinBox()
        self.update_interval_spin.setRange(1, 168)  # 1小时到7天
        self.update_interval_spin.setValue(self.config.update_check_interval)
        self.update_interval_spin.setMinimumHeight(35)
        self.update_interval_spin.setStyleSheet("""
            QSpinBox {
                border: 2px solid #ddd;
                border-radius: 6px;
                padding: 8px;
                background-color: white;
                font-size: 14px;
            }
            QSpinBox:focus {
                border-color: #4CAF50;
            }
        """)
        update_layout.addWidget(self.update_interval_spin, 1, 1)

        # 立即检查更新按钮
        check_update_button = QPushButton("🔍 立即检查更新")
        check_update_button.setMinimumHeight(40)
        check_update_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        check_update_button.clicked.connect(self.check_update)
        update_layout.addWidget(check_update_button, 2, 0, 1, 2)

        update_group.setLayout(update_layout)
        layout.addWidget(update_group)

        # 激活信息组
        activation_group = QGroupBox("🔐 激活信息")
        activation_layout = QGridLayout()
        activation_layout.setSpacing(10)

        # 显示当前激活码
        activation_layout.addWidget(QLabel("🔑 当前激活码:"), 0, 0)
        self.token_display = QLineEdit()
        saved_token = self.activation_manager.load_saved_token()
        self.token_display.setText(saved_token if saved_token else "未激活")
        self.token_display.setReadOnly(True)
        self.token_display.setMinimumHeight(35)
        self.token_display.setStyleSheet("""
            QLineEdit {
                background-color: #f5f5f5;
                border: 2px solid #ddd;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        activation_layout.addWidget(self.token_display, 0, 1)

        # 添加刷新按钮
        refresh_button = QPushButton("🔄 刷新激活状态")
        refresh_button.setMinimumHeight(40)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        refresh_button.clicked.connect(self.refresh_activation_status)
        activation_layout.addWidget(refresh_button, 1, 0, 1, 2)

        activation_group.setLayout(activation_layout)
        layout.addWidget(activation_group)

        # 保存配置
        save_button = QPushButton("💾 保存设置")
        save_button.setMinimumHeight(45)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        save_button.clicked.connect(self.save_config)

        # 重置配置
        reset_button = QPushButton("🔄 重置默认")
        reset_button.setMinimumHeight(45)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        reset_button.clicked.connect(self.reset_config)

        # 添加按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(reset_button)
        layout.addLayout(button_layout)

        layout.addStretch()

    def setup_about_tab(self, tab):
        """设置关于选项卡"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 添加标题和图标
        title_layout = QHBoxLayout()
        logo_label = QLabel()
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "img", "logo.png")
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            logo_label.setPixmap(pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            logo_label.setText("📚")
            logo_label.setStyleSheet("font-size: 64px;")
        logo_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(logo_label)

        title_info_layout = QVBoxLayout()
        title_label = QLabel("Useful Novel Tool")
        title_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title_label.setStyleSheet("color: #2E7D32; margin-bottom: 5px;")
        title_info_layout.addWidget(title_label)

        subtitle_label = QLabel("实用小说下载工具")
        subtitle_label.setFont(QFont("Microsoft YaHei", 14))
        subtitle_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        title_info_layout.addWidget(subtitle_label)

        title_layout.addLayout(title_info_layout)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 版本信息卡片
        version_card = QGroupBox()
        version_card.setStyleSheet("""
            QGroupBox {
                background-color: #E8F5E8;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                margin: 10px;
                padding: 15px;
            }
        """)
        version_layout = QVBoxLayout(version_card)

        version_label = QLabel(f"🏷️ 版本: v{VERSION}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        version_label.setStyleSheet("color: #2E7D32; margin: 5px;")
        version_layout.addWidget(version_label)

        # 激活信息
        self.about_activation_info = QLabel()
        self.update_about_activation_info()
        self.about_activation_info.setAlignment(Qt.AlignCenter)
        self.about_activation_info.setFont(QFont("Microsoft YaHei", 12))
        self.about_activation_info.setStyleSheet("margin: 5px;")
        version_layout.addWidget(self.about_activation_info)

        layout.addWidget(version_card)

        # 检查更新按钮
        self.check_update_button = QPushButton("🔍 检查更新")
        self.check_update_button.setMinimumHeight(45)
        self.check_update_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.check_update_button.clicked.connect(self.check_update)
        layout.addWidget(self.check_update_button)

        # 说明文本
        desc_text = QTextEdit()
        desc_text.setReadOnly(True)
        desc_text.setMaximumHeight(400)
        desc_text.setStyleSheet("""
            QTextEdit {
                background-color: #FAFAFA;
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
                font-size: 14px;
                line-height: 1.6;
            }
        """)
        desc_text.setHtml("""
        <div style='font-family: Microsoft YaHei; line-height: 1.8;'>
            <h3 style='color: #2E7D32; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;'>🛠️ 软件说明</h3>
            <p style='margin: 15px 0;'><strong>Useful Novel Tool</strong> 是一款功能强大的小说下载工具，支持某茄小说和某猫小说平台。</p>

            <h4 style='color: #1976D2; margin-top: 20px;'>✨ 主要特性：</h4>
            <ul style='margin: 10px 0; padding-left: 20px;'>
                <li style='margin: 8px 0;'>📚 支持多平台小说下载（某茄小说、某猫小说）</li>
                <li style='margin: 8px 0;'>⚡ 多线程并发下载，提高下载效率</li>
                <li style='margin: 8px 0;'>📄 支持TXT和EPUB格式导出</li>
                <li style='margin: 8px 0;'>🎨 现代化的用户界面，操作简单直观</li>
                <li style='margin: 8px 0;'>📊 智能章节管理和进度跟踪</li>
                <li style='margin: 8px 0;'>🔄 自动更新检查和版本管理</li>
            </ul>

            <h4 style='color: #1976D2; margin-top: 20px;'>📖 使用方法：</h4>
            <ol style='margin: 10px 0; padding-left: 20px;'>
                <li style='margin: 8px 0;'>选择对应的小说平台选项卡</li>
                <li style='margin: 8px 0;'>输入小说ID或分享链接</li>
                <li style='margin: 8px 0;'>选择保存路径和格式</li>
                <li style='margin: 8px 0;'>点击"开始下载"按钮</li>
            </ol>

            <h4 style='color: #F57C00; margin-top: 20px;'>⚠️ 注意事项：</h4>
            <p style='margin: 10px 0; color: #666;'>本软件仅供学习和研究使用，请勿用于任何商业用途。</p>
            <p style='margin: 10px 0; color: #666;'>请遵守相关法律法规，尊重版权所有者的权益。</p>
            <p style='margin: 10px 0; color: #666;'>下载的内容仅供个人阅读，请支持正版。</p>
        </div>
        """)
        layout.addWidget(desc_text)

    def setup_sponsor_tab(self, tab):
        """设置赞助选项卡"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 添加标题
        title_label = QLabel("☕ 请作者喝杯奶茶")
        title_label.setFont(QFont("Microsoft YaHei", 28, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            margin: 20px 0;
            color: #2E7D32;
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 #E8F5E8, stop: 1 #C8E6C9);
            border-radius: 15px;
            padding: 15px;
        """)
        layout.addWidget(title_label)

        # 添加赞助说明文字
        sponsor_text = QTextEdit()
        sponsor_text.setReadOnly(True)
        sponsor_text.setMaximumHeight(200)
        sponsor_text.setMinimumHeight(180)
        sponsor_text.setStyleSheet("""
            background-color: #FFF8E1;
            border: 2px solid #FFB74D;
            border-radius: 12px;
            padding: 15px;
            font-family: Microsoft YaHei;
        """)
        sponsor_text.setHtml("""
        <div style='text-align: center; font-family: Microsoft YaHei;'>
            <p style='font-size: 18px; line-height: 1.8; color: #333; margin: 10px 0;'>
                💝 感谢您使用 <strong>Useful Novel Tool</strong>！
            </p>
            <p style='font-size: 16px; line-height: 1.6; color: #555; margin: 10px 0;'>
                如果这款软件对您有所帮助，可以考虑请作者喝杯奶茶☕<br>
                支持软件的持续更新与维护 🔧
            </p>
            <p style='font-size: 16px; line-height: 1.6; color: #666; margin: 10px 0;'>
                您的每一份赞助都是作者继续前进的动力！💪
            </p>
            <p style='font-size: 18px; line-height: 1.5; color: #FF6600; font-weight: bold; margin-top: 15px;'>
                📱 扫描下方二维码进行赞助，金额随意，感谢您的支持！🙏
            </p>
        </div>
        """)
        layout.addWidget(sponsor_text)

        # 创建收款码显示区域
        qrcode_layout = QHBoxLayout()
        qrcode_layout.setSpacing(30)  # 增加间距
        qrcode_layout.setContentsMargins(20, 10, 20, 10)  # 设置边距

        # 支付宝收款码
        alipay_group = QGroupBox("💙 支付宝")
        alipay_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #1678FF;
                border: 3px solid #1678FF;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F0F8FF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #F0F8FF;
            }
        """)
        alipay_layout = QVBoxLayout(alipay_group)
        alipay_layout.setContentsMargins(20, 25, 20, 20)
        self.alipay_qrcode = QLabel()
        self.alipay_qrcode.setAlignment(Qt.AlignCenter)
        self.alipay_qrcode.setMinimumSize(300, 300)
        self.alipay_qrcode.setText("📱\n支付宝收款码\n加载中...")
        self.alipay_qrcode.setStyleSheet("""
            border: 3px dashed #1678FF;
            border-radius: 12px;
            padding: 10px;
            background-color: white;
            color: #1678FF;
            font-size: 16px;
            font-weight: bold;
        """)
        alipay_layout.addWidget(self.alipay_qrcode)
        qrcode_layout.addWidget(alipay_group)

        # 微信收款码
        wechat_group = QGroupBox("💚 微信")
        wechat_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #07C160;
                border: 3px solid #07C160;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F0FFF0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #F0FFF0;
            }
        """)
        wechat_layout = QVBoxLayout(wechat_group)
        wechat_layout.setContentsMargins(20, 25, 20, 20)
        self.wechat_qrcode = QLabel()
        self.wechat_qrcode.setAlignment(Qt.AlignCenter)
        self.wechat_qrcode.setMinimumSize(300, 300)
        self.wechat_qrcode.setText("📱\n微信收款码\n加载中...")
        self.wechat_qrcode.setStyleSheet("""
            border: 3px dashed #07C160;
            border-radius: 12px;
            padding: 10px;
            background-color: white;
            color: #07C160;
            font-size: 16px;
            font-weight: bold;
        """)
        wechat_layout.addWidget(self.wechat_qrcode)
        qrcode_layout.addWidget(wechat_group)

        layout.addLayout(qrcode_layout)

        # 添加提示信息
        # note_label = QLabel("赞助后可以加入用户群，获取更多功能和技术支持")
        # note_label.setAlignment(Qt.AlignCenter)
        # note_label.setStyleSheet("color: #666; margin-top: 20px; font-size: 15px;")
        # layout.addWidget(note_label)

        # 加载收款码图片
        self.load_qrcode_images()

        layout.addStretch(1)

    def update_about_activation_info(self):
        """更新关于页面的激活信息"""
        token = self.activation_manager.load_saved_token()
        if token and self.activation_manager.is_activated():
            masked_token = f"{token[:4]}...{token[-4:]}" if len(token) > 8 else token
            self.about_activation_info.setText(f"激活状态: 已激活 (激活码: {masked_token})")
            self.about_activation_info.setStyleSheet("color: green;")
        else:
            self.about_activation_info.setText("激活状态: 未激活")
            self.about_activation_info.setStyleSheet("color: red;")

    def load_qrcode_images(self):
        """加载收款码图片"""
        try:
            # 获取图片目录路径
            img_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "img")

            # 加载支付宝收款码
            alipay_path = os.path.join(img_dir, "alipay_qrcode.png")
            if os.path.exists(alipay_path):
                alipay_pixmap = QPixmap(alipay_path)
                if not alipay_pixmap.isNull():
                    alipay_pixmap = alipay_pixmap.scaled(
                        280, 280,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.alipay_qrcode.setPixmap(alipay_pixmap)
                    self.logger.info("已加载支付宝收款码")
                else:
                    self.logger.warning("支付宝收款码图片加载失败")
            else:
                self.logger.warning(f"支付宝收款码图片不存在: {alipay_path}")

            # 加载微信收款码
            wechat_path = os.path.join(img_dir, "wechat_qrcode.png")
            if os.path.exists(wechat_path):
                wechat_pixmap = QPixmap(wechat_path)
                if not wechat_pixmap.isNull():
                    wechat_pixmap = wechat_pixmap.scaled(
                        280, 280,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.wechat_qrcode.setPixmap(wechat_pixmap)
                    self.logger.info("已加载微信收款码")
                else:
                    self.logger.warning("微信收款码图片加载失败")
            else:
                self.logger.warning(f"微信收款码图片不存在: {wechat_path}")
        except Exception as e:
            self.logger.error(f"加载收款码图片时出错: {str(e)}")

    def browse_save_path(self):
        """浏览并选择保存路径"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择保存目录", self.save_path_input.text()
        )
        if directory:
            self.save_path_input.setText(directory)

    def start_download(self):
        """开始下载小说"""
        # 验证激活码是否可用
        token = self.activation_manager.load_saved_token()
        if not token:
            QMessageBox.warning(self, "激活验证", "您尚未激活软件，请先激活后再使用")
            return

        # 获取用户输入
        book_id_input = self.novel_id_input.text().strip()
        save_path = self.save_path_input.text().strip()
        # 根据选择的索引确定格式
        format_choice = "txt" if self.format_combo.currentIndex() == 0 else "epub"

        # 检查输入
        if not book_id_input:
            QMessageBox.warning(self, "输入错误", "请输入小说ID或链接")
            return

        if not save_path:
            QMessageBox.warning(self, "输入错误", "请选择保存路径")
            return

        # 更新配置
        self.config.novel_format = format_choice
        self.config.save()

        # 提取book_id
        book_id = None

        # 尝试从URL中提取
        # 检查是否为URL
        urls = re.findall(r"(https?://[^\s]+)", book_id_input)
        if urls:
            url_str = urls[0]
            parsed = urlparse(url_str)
            # 尝试解析 /page/<book_id> 模式
            m = re.search(r"/page/(\d+)", parsed.path)
            if m:
                book_id = m.group(1)
            else:
                # 从 query 参数中尝试获取 book_id 或 bookId
                qs = parse_qs(parsed.query)
                bid_list = qs.get("book_id") or qs.get("bookId")
                if bid_list:
                    book_id = bid_list[0]

            if not book_id:
                QMessageBox.warning(self, "输入错误", "无法从链接中解析出book_id，请检查链接格式")
                return
        # 检查是否为纯数字ID
        elif book_id_input.isdigit():
            book_id = book_id_input
        else:
            QMessageBox.warning(self, "输入错误", "请输入有效的小说ID或链接")
            return

        # 禁用下载按钮，启用取消按钮
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 清空书籍信息显示
        self.book_info_text.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备下载...")

        # 使用临时下载器获取章节列表
        downloader = ChapterDownloader(book_id, self.network)
        chapters = downloader.fetch_chapter_list()

        if not chapters:
            self.status_label.setText("获取章节列表失败")
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            return

        # 获取书籍信息
        try:
            book_info_url = f"https://fanqienovel.com/page/{book_id}"
            response = self.network.session.get(
                book_info_url,
                headers=self.network.get_headers(),
                timeout=self.config.request_timeout,
            )

            if response.status_code == 404:
                self.status_label.setText(f"小说ID {book_id} 不存在！")
                self.download_button.setEnabled(True)
                self.cancel_button.setEnabled(False)
                return

            response.raise_for_status()

            # 解析书籍信息
            book_name, author, description, tags, chapter_count = ContentParser.parse_book_info(response.text)

            # 加载并显示封面图片
            cover_path = os.path.join(self.config.default_save_dir, f"{book_name}.jpg")
            if os.path.exists(cover_path):
                pixmap = QPixmap(cover_path)
                if not pixmap.isNull():
                    # 按比例缩放图片以适应标签大小
                    pixmap = pixmap.scaled(
                        self.cover_label.width(),
                        self.cover_label.height(),
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.cover_label.setPixmap(pixmap)
                else:
                    self.cover_label.setText("封面加载失败")
            else:
                self.cover_label.setText("暂无封面")

        except Exception as e:
            self.logger.error(f"获取书籍信息失败: {str(e)}")
            book_name = f"未知书籍_{book_id}"
            author = "未知作者"
            description = "无简介"
            tags = []
            chapter_count = len(chapters) if chapters else 0


        # 显示书籍信息
        self.book_info_text.setHtml(f"""
        <h3>{book_name}</h3>
        <p><b>作者:</b> {author}</p>
        <p><b>章节数:</b> {chapter_count}</p>
        <p><b>标签:</b> {', '.join(tags) if tags else '无'}</p>
        <p><b>简介:</b> {description}</p>
        """)

        # 保存当前小说ID，用于查询任务状态
        self.current_novel_id = book_id

        # 构造章节ID列表和章节标题列表（如果是某茄小说ID）
        chapter_ids = None
        chapter_titles = None
        if len(book_id) > 9:
            # 提取章节ID和标题
            chapter_ids = [ch['id'] for ch in chapters]
            chapter_titles = [ch['title'] for ch in chapters]
            self.logger.info(f"提取到{len(chapter_ids)}个章节ID和{len(chapter_titles)}个章节标题")

        # 保存章节标题列表和校对选项，用于下载完成后的校对
        self.current_chapter_titles = chapter_titles if chapter_titles else []
        self.current_enable_proofread = self.chapter_proofread_checkbox.isChecked()
        self.current_book_name = book_name
        self.current_save_path = save_path
        self.current_format_choice = format_choice

        # 启动异步下载任务
        self.status_label.setText("正在提交下载任务...")

        # 启动下载任务，并设置状态变更回调
        self.task_manager.start_download_task(
            token=token,
            novel_id=book_id,
            novel_name=book_name,
            novel_author=author,
            novel_desc=description,
            chapter_ids=chapter_ids,
            chapter_titles=chapter_titles,
            format_choice=format_choice,  # 传递用户选择的格式
            on_status_change=self.update_task_status
        )

        # 启动任务状态查询定时器
        if self.task_timer is None:
            self.task_timer = QTimer(self)
            self.task_timer.timeout.connect(self.check_task_status)
            self.task_timer.start(1000)  # 每秒查询一次任务状态

        self.status_label.setText("已提交下载任务，正在处理中...")
        self.progress_bar.setValue(5)

    def check_task_status(self):
        """查询当前任务状态"""
        if not self.current_novel_id:
            return

        # 获取任务状态
        task_status = self.task_manager.get_task_status(self.current_novel_id)
        if not task_status:
            return

        # 更新UI
        self.update_task_status(task_status)

    def update_task_status(self, task_status):
        """更新任务状态

        Args:
            task_status: 任务状态字典
        """
        if not task_status:
            return

        status = task_status.get("status", "")
        progress = task_status.get("progress", 0)

        # 使用QMetaObject.invokeMethod确保在主线程中更新UI
        QMetaObject.invokeMethod(
            self,
            "_update_ui_task_status",
            Qt.QueuedConnection,
            Q_ARG(str, status),
            Q_ARG(int, progress),
            Q_ARG(dict, task_status)
        )

        # 统一提前处理终止分支，防止后续UI访问已销毁对象
        if status in (DownloadTaskStatus.COMPLETED, DownloadTaskStatus.FAILED, DownloadTaskStatus.CANCELED):
            # 停止并清理定时器
            if self.task_timer:
                self.task_timer.stop()
                self.task_timer.deleteLater()
                self.task_timer = None
            # 清除当前任务ID
            self.current_novel_id = None

    @pyqtSlot(str, int, dict)
    def _update_ui_task_status(self, status, progress, task_status):
        """在主线程中更新UI

        Args:
            status: 任务状态
            progress: 进度百分比
            task_status: 完整的任务状态字典
        """
        # 更新进度条和状态
        self.progress_bar.setValue(progress)

        if status == DownloadTaskStatus.PENDING:
            self.status_label.setText("等待下载...")
        elif status == DownloadTaskStatus.PROCESSING:
            error_message = task_status.get("error_message", "")
            if error_message:
                self.status_label.setText(f"处理中: {error_message}")
            else:
                self.status_label.setText(f"下载中... {progress}%")
        elif status == DownloadTaskStatus.COMPLETED:
            self.status_label.setText("下载完成！")
            # 恢复按钮状态
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            # 处理下载完成的文件 - 使用延迟处理，避免UI阻塞
            QTimer.singleShot(100, lambda: self._process_downloaded_file(task_status))
        elif status == DownloadTaskStatus.FAILED:
            error_message = task_status.get("error_message", "未知错误")
            self.status_label.setText(f"下载失败: {error_message}")
            # 使用QTimer延迟显示错误对话框，避免UI冲突
            QTimer.singleShot(100, lambda: self._show_download_failed_message(error_message))
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
        elif status == DownloadTaskStatus.CANCELED:
            self.status_label.setText("下载已取消")
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

    def _process_downloaded_file(self, task_status):
        """处理下载完成的文件

        Args:
            task_status: 任务状态字典
        """
        file_url = task_status.get("file_url", "")
        if file_url and os.path.exists(file_url):
            try:
                save_dir = self.save_path_input.text().strip()
                format_choice = "txt" if self.format_combo.currentIndex() == 0 else "epub"
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir, exist_ok=True)
                    self.logger.info(f"创建保存目录: {save_dir}")
                original_filename = os.path.basename(file_url)
                base_name = os.path.splitext(original_filename)[0]

                # 检查下载的文件格式和用户期望的格式
                downloaded_is_epub = file_url.lower().endswith('.epub')
                user_wants_epub = format_choice == "epub"

                if user_wants_epub:
                    save_ext = ".epub"
                    # 只有当下载的是TXT但用户要EPUB时才需要转换
                    self.need_convert_to_epub = not downloaded_is_epub
                else:
                    save_ext = ".txt"
                    self.need_convert_to_epub = False

                safe_base_name = "".join(c if c.isalnum() or c in [' ', '_', '-'] else '_' for c in base_name)
                target_path = os.path.join(save_dir, safe_base_name + save_ext)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                if self.need_convert_to_epub and file_url.lower().endswith('.txt'):
                    # 在转换为EPUB之前先进行章节校对
                    if (hasattr(self, 'current_enable_proofread') and
                        self.current_enable_proofread and
                        hasattr(self, 'current_chapter_titles') and
                        self.current_chapter_titles):

                        self.logger.info("在转换为EPUB之前先进行章节校对...")
                        from ..book_parser.chapter_proofreader import ChapterProofreader
                        proofreader = ChapterProofreader()
                        proofreader.proofread_novel(file_url, self.current_chapter_titles)

                    from ..book_parser.epub_generator import EpubGenerator
                    self.logger.info(f"转换文件格式: {file_url} -> {target_path}")

                    # 获取书籍基本信息
                    novel_name = task_status.get("novel_name", safe_base_name)

                    # 读取TXT内容，尝试多种编码
                    content = None
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']

                    for encoding in encodings:
                        try:
                            with open(file_url, 'r', encoding=encoding) as txt_file:
                                content = txt_file.read()
                            self.logger.info(f"成功使用 {encoding} 编码读取文件")
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            self.logger.warning(f"尝试使用 {encoding} 编码读取文件时出错: {str(e)}")
                            continue

                    # 如果所有编码都失败，使用二进制读取然后尝试强制解码
                    if content is None:
                        self.logger.warning("所有编码都失败，尝试强制解码")
                        with open(file_url, 'rb') as f:
                            raw_content = f.read()
                            # 尝试从二进制数据强制解码
                            content = raw_content.decode('utf-8', errors='replace')

                    # 解析元数据
                    author = task_status.get("novel_author", "未知作者")
                    description = task_status.get("novel_desc", "")

                    # 如果任务状态中没有作者或描述信息，尝试从TXT内容中提取
                    if not author or not description:
                        lines = content.split('\n')
                        if len(lines) > 5:
                            for i, line in enumerate(lines[:15]):  # 检查更多行
                                if ("作者" in line or "著" in line) and i < 10:
                                    author = line.replace("作者：", "").replace("作者:", "").replace("著：", "").replace("著:", "").strip()
                                if ("简介" in line or "内容简介" in line or "内容提要" in line) and i < 15:
                                    desc_start = i
                                    # 提取更多行作为描述
                                    desc_lines = []
                                    for j in range(desc_start, min(desc_start + 10, len(lines))):
                                        if len(lines[j].strip()) > 0 and "章" not in lines[j] and "卷" not in lines[j]:
                                            desc_lines.append(lines[j].strip())
                                        else:
                                            break
                                    description = "\n".join(desc_lines)

                    # 创建EPUB生成器
                    generator = EpubGenerator(
                        identifier=novel_name,  # 使用书名作为标识符
                        title=novel_name,
                        language="zh-CN",
                        author=author,
                        description=description,
                        publisher="Useful Novel Tool"
                    )

                    # 生成EPUB
                    success = generator.generate_from_text(content, novel_name, author, description, target_path)

                    if success:
                        self.logger.info(f"EPUB转换完成: {target_path}")
                        # 删除原始TXT文件
                        try:
                            os.remove(file_url)
                            self.logger.info(f"已删除原始TXT文件: {file_url}")
                        except Exception as e:
                            self.logger.warning(f"删除原始TXT文件失败: {str(e)}")
                        file_url = target_path
                    else:
                        self.logger.error("EPUB转换失败，将使用原始TXT文件")
                        # 复制TXT文件到目标路径
                        import shutil
                        target_txt_path = os.path.join(save_dir, safe_base_name + ".txt")
                        shutil.copy2(file_url, target_txt_path)
                        self.logger.info(f"复制文件: {file_url} -> {target_txt_path}")
                        file_url = target_txt_path
                elif file_url != target_path and os.path.exists(file_url):
                    # 如果服务器返回的文件格式已经是用户期望的格式，直接复制
                    if os.path.exists(target_path):
                        timestamp = int(time.time())
                        target_path = os.path.join(
                            save_dir,
                            f"{safe_base_name}_{timestamp}{save_ext}"
                        )
                    import shutil
                    shutil.copy2(file_url, target_path)
                    self.logger.info(f"复制文件: {file_url} -> {target_path}")
                    # 删除原始文件，避免重复文件
                    try:
                        os.remove(file_url)
                        self.logger.info(f"已删除原始文件: {file_url}")
                    except Exception as e:
                        self.logger.warning(f"删除原始文件失败: {str(e)}")
                    file_url = target_path

                download_result = {
                    "success": 1,
                    "failed": 0,
                    "canceled": 0,
                    "time_cost": 0,
                    "book_name": task_status.get("novel_name", os.path.basename(file_url)),
                    "merged_path": file_url if file_url.lower().endswith('.txt') else None,
                    "epub_path": file_url if file_url.lower().endswith('.epub') else None,
                    "from_server": True
                }

                # 先执行章节校对（如果启用）
                # 注意：无论是TXT还是EPUB格式都需要校对，因为EPUB是从TXT转换而来的
                if (hasattr(self, 'current_enable_proofread') and
                    self.current_enable_proofread and
                    hasattr(self, 'current_chapter_titles') and
                    self.current_chapter_titles and
                    file_url.lower().endswith('.txt')):  # 只对TXT文件进行校对

                    QTimer.singleShot(100, lambda: self._perform_server_download_proofread(file_url))

                # 使用QTimer延迟显示完成消息，避免UI冲突
                QTimer.singleShot(300, lambda: self._show_download_complete_message(file_url))

                # 使用QTimer延迟调用download_finished，避免递归调用导致的堆栈问题
                QTimer.singleShot(400, lambda: self.download_finished(download_result))

            except Exception as e:
                self.logger.error(f"处理下载文件时出错: {str(e)}")
                # 使用QTimer延迟显示错误对话框，避免UI冲突
                QTimer.singleShot(100, lambda: QMessageBox.critical(
                    self,
                    "处理错误",
                    f"处理下载文件时出错: {str(e)}",
                    QMessageBox.Ok
                ))
                # 恢复按钮状态，防止UI卡死
                self.download_button.setEnabled(True)
                self.cancel_button.setEnabled(False)
        else:
            error_msg = "下载完成，但未获取到有效的文件路径"
            if file_url:
                error_msg = f"下载完成，但文件不存在: {file_url}"
            self.logger.error(error_msg)
            # 使用QTimer延迟显示错误对话框，避免UI冲突
            QTimer.singleShot(100, lambda: QMessageBox.warning(
                self,
                "文件缺失",
                error_msg,
                QMessageBox.Ok
            ))
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

    def _show_download_failed_message(self, error_message):
        """显示下载失败消息

        Args:
            error_message: 错误信息
        """
        try:
            QMessageBox.critical(
                self,
                "下载失败",
                f"下载任务失败: {error_message}",
                QMessageBox.Ok
            )
        except Exception as e:
            self.logger.error(f"显示下载失败消息时出错: {str(e)}")

    @pyqtSlot(str)
    def _show_download_complete_message(self, file_path):
        """显示下载完成消息

        Args:
            file_path: 文件路径
        """
        try:
            reply = QMessageBox.information(
                self,
                "下载完成",
                f"文件已成功下载到:\n{file_path}\n\n是否要打开所在文件夹？",
                QMessageBox.Yes | QMessageBox.No
            )

            # 打开文件所在文件夹，但只在用户确认时进行
            if reply == QMessageBox.Yes:
                try:
                    # 确保路径存在再尝试打开
                    file_dir = os.path.dirname(file_path)
                    if os.path.exists(file_dir):
                        # 使用更安全的方式打开文件夹
                        if os.name == 'nt':  # Windows
                            os.startfile(file_dir)
                        elif os.name == 'posix':  # macOS 和 Linux
                            import subprocess
                            # 对于macOS，使用open命令
                            if sys.platform == 'darwin':
                                subprocess.run(['open', file_dir])
                            # 对于Linux，使用xdg-open命令
                            else:
                                subprocess.run(['xdg-open', file_dir])
                        self.logger.info(f"已打开文件夹: {file_dir}")
                    else:
                        self.logger.warning(f"文件夹不存在，无法打开: {file_dir}")
                        QMessageBox.warning(
                            self,
                            "无法打开文件夹",
                            f"指定的文件夹不存在: {file_dir}"
                        )
                except Exception as e:
                    self.logger.error(f"打开文件夹失败: {str(e)}")
                    QMessageBox.warning(
                        self,
                        "打开失败",
                        f"无法打开文件夹: {str(e)}"
                    )
        except Exception as e:
            self.logger.error(f"显示下载完成消息时出错: {str(e)}")

    def download_finished(self, result):
        """下载完成回调"""
        try:
            success = result.get("success", 0)
            failed = result.get("failed", 0)
            canceled = result.get("canceled", 0)
            time_cost = result.get("time_cost", 0)
            book_name = result.get("book_name", "未知书籍")
            from_server = result.get("from_server", False)

            # 执行章节校对（如果启用）
            # 注意：EPUB格式也需要校对，因为EPUB是从TXT转换而来的
            if (hasattr(self, 'current_enable_proofread') and
                self.current_enable_proofread and
                hasattr(self, 'current_chapter_titles') and
                self.current_chapter_titles):

                self._perform_chapter_proofread(result)

            # 更新UI
            self.progress_bar.setValue(100)
            self.status_label.setText(f"下载完成！用时 {time_cost:.1f} 秒")

            # 只有在非服务器下载模式下才显示结果对话框
            # 服务器下载模式已经在_show_download_complete_message中显示了对话框
            if not from_server:
                message = f"《{book_name}》下载完成！\n"
                message += f"成功: {success} 章\n"
                message += f"失败: {failed} 章\n"
                message += f"取消: {canceled} 章\n"
                message += f"用时: {time_cost:.1f} 秒"

                # 使用QTimer延迟显示完成对话框，避免UI冲突
                QTimer.singleShot(300, lambda: QMessageBox.information(
                    self,
                    "下载完成",
                    message
                ))

            # 恢复按钮状态
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
        except Exception as e:
            self.logger.error(f"处理下载完成回调时出错: {str(e)}")
            # 确保按钮状态恢复，即使发生错误
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

    def _perform_chapter_proofread(self, result):
        """执行章节校对"""
        try:
            # 获取下载的文件路径
            txt_file_path = None

            # 从result中获取TXT文件路径
            if result.get("merged_path"):
                txt_file_path = result["merged_path"]
            elif result.get("from_server", False):
                # 服务器下载模式，需要构造文件路径
                if hasattr(self, 'current_save_path') and hasattr(self, 'current_book_name'):
                    safe_book_name = "".join(c for c in self.current_book_name if c.isalnum() or c in (' ', '-', '_')).rstrip()

                    # 对于EPUB格式，先尝试找TXT文件进行校对
                    if hasattr(self, 'current_format_choice') and self.current_format_choice == 'epub':
                        # EPUB格式：先查找TXT文件，如果不存在则跳过校对
                        txt_file_path = os.path.join(self.current_save_path, f"{safe_book_name}.txt")
                        if not os.path.exists(txt_file_path):
                            # 尝试查找可能的TXT文件
                            epub_file_path = os.path.join(self.current_save_path, f"{safe_book_name}.epub")
                            if os.path.exists(epub_file_path):
                                self.logger.info("EPUB文件已存在，但未找到对应的TXT文件，跳过校对")
                                return
                    else:
                        # TXT格式
                        txt_file_path = os.path.join(self.current_save_path, f"{safe_book_name}.txt")

            if not txt_file_path or not os.path.exists(txt_file_path):
                self.logger.warning(f"无法找到TXT文件进行校对: {txt_file_path}")
                return

            self.logger.info(f"开始执行章节校对: {txt_file_path}")

            # 显示校对开始提示
            self.status_label.setText("🔧 正在校对章节顺序...")
            self.progress_bar.setValue(95)  # 设置为95%，表示即将完成

            # 显示校对进度对话框
            progress_msg = QMessageBox(self)
            progress_msg.setWindowTitle("章节校对")
            progress_msg.setText(f"正在校对《{self.current_book_name}》的章节顺序...\n\n"
                                f"📖 期望章节数量: {len(self.current_chapter_titles)}\n"
                                f"📁 文件路径: {os.path.basename(txt_file_path)}\n\n"
                                f"⏳ 请稍候，正在分析章节结构...")
            progress_msg.setStandardButtons(QMessageBox.NoButton)
            progress_msg.show()

            # 处理事件，确保对话框显示
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

            success = False
            try:
                # 导入并使用章节校对器
                from ..book_parser.chapter_proofreader import ChapterProofreader
                proofreader = ChapterProofreader()

                # 执行校对
                success = proofreader.proofread_novel(txt_file_path, self.current_chapter_titles)

            finally:
                # 确保进度对话框一定会关闭
                try:
                    progress_msg.close()
                    progress_msg.deleteLater()  # 确保对象被正确清理
                except Exception as e:
                    self.logger.warning(f"关闭校对进度对话框时出错: {str(e)}")

                # 处理事件，确保对话框关闭
                QApplication.processEvents()

            if success:
                self.logger.info("章节校对完成")
                self.status_label.setText("✅ 章节校对完成！")

                # 显示详细的校对结果
                QTimer.singleShot(100, lambda: QMessageBox.information(
                    self,
                    "✅ 章节校对完成",
                    f"《{self.current_book_name}》章节顺序校对完成！\n\n"
                    f"📊 校对统计:\n"
                    f"• 期望章节数量: {len(self.current_chapter_titles)}\n"
                    f"• 校对方式: 智能匹配重排序\n"
                    f"• 备份文件: {os.path.basename(txt_file_path)}.backup\n\n"
                    f"💡 提示: 原文件已自动备份，如有问题可恢复。",
                    QMessageBox.Ok
                ))
            else:
                self.logger.warning("章节校对失败")
                self.status_label.setText("❌ 章节校对失败")

                QTimer.singleShot(100, lambda: QMessageBox.warning(
                    self,
                    "❌ 章节校对失败",
                    f"《{self.current_book_name}》章节顺序校对失败！\n\n"
                    f"可能的原因:\n"
                    f"• 文件格式不支持\n"
                    f"• 章节标题格式异常\n"
                    f"• 文件读取权限问题\n\n"
                    f"💡 建议: 请检查日志获取详细错误信息。",
                    QMessageBox.Ok
                ))

        except Exception as e:
            self.logger.error(f"执行章节校对时出错: {str(e)}")
            self.status_label.setText("❌ 校对过程出错")

            QTimer.singleShot(100, lambda: QMessageBox.critical(
                self,
                "❌ 校对过程出错",
                f"执行章节校对时发生错误:\n\n"
                f"错误信息: {str(e)}\n\n"
                f"💡 建议: 请重试或联系技术支持。",
                QMessageBox.Ok
            ))

    def _perform_server_download_proofread(self, file_path):
        """执行服务器下载文件的章节校对"""
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"无法找到文件进行校对: {file_path}")
                return

            self.logger.info(f"开始执行服务器下载文件的章节校对: {file_path}")

            # 显示简单的校对提示（服务器下载模式）
            self.status_label.setText("🔧 正在校对章节顺序...")

            # 导入并使用章节校对器
            from ..book_parser.chapter_proofreader import ChapterProofreader
            proofreader = ChapterProofreader()

            # 执行校对
            success = proofreader.proofread_novel(file_path, self.current_chapter_titles)

            if success:
                self.logger.info("服务器下载文件章节校对完成")
                self.status_label.setText("✅ 下载并校对完成！")
            else:
                self.logger.warning("服务器下载文件章节校对失败")
                self.status_label.setText("⚠️ 下载完成，校对失败")

        except Exception as e:
            self.logger.error(f"执行服务器下载文件章节校对时出错: {str(e)}")
            self.status_label.setText("⚠️ 下载完成，校对出错")

    def download_error(self, error_msg):
        """下载错误回调"""
        QMessageBox.critical(self, "下载错误", error_msg)
        self.status_label.setText(f"错误: {error_msg}")

        # 恢复按钮状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

    def save_config(self):
        """保存配置"""
        # 从UI获取配置值
        save_path = self.config_save_path_input.text().strip()
        qimao_save_dir = self.config_qimao_save_path_input.text().strip()

        # 确保路径存在或可以创建
        try:
            if save_path:
                os.makedirs(save_path, exist_ok=True)
                self.config.save_path = save_path

            if qimao_save_dir:
                os.makedirs(qimao_save_dir, exist_ok=True)
                self.config.qimao_save_dir = qimao_save_dir

            # 更新其他配置
            self.config.auto_check_update = self.auto_check_update.isChecked()
            self.config.update_check_interval = self.update_interval_spin.value()
            self.config.novel_format = self.format_combo.currentText().lower()

            # 保存配置
            self.config.save()

            # 更新UI上的路径，使其与保存的配置保持一致
            self.save_path_input.setText(str(self.config.default_save_dir))
            self.qimao_save_path.setText(qimao_save_dir if qimao_save_dir else
                                       os.path.join(os.path.expanduser("~"), "Downloads", "某猫小说"))

            # 显示成功消息
            QMessageBox.information(self, "保存成功", "配置已成功保存")
            self.logger.info("配置已成功保存")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"配置保存失败: {str(e)}")
            self.logger.error(f"配置保存失败: {str(e)}")

    def reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有配置到默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 重置配置
                self.auto_check_update.setChecked(True)
                self.update_interval_spin.setValue(24)

                # 重置默认保存路径 - 使用当前工作目录
                default_path = str(Path(os.getcwd()))
                self.config_save_path_input.setText(default_path)

                # 重置某猫小说保存路径
                default_qimao_path = os.path.join(os.path.expanduser("~"), "Downloads", "某猫小说")
                self.config_qimao_save_path_input.setText(default_qimao_path)

                # 重置保存格式
                index = self.format_combo.findText("txt")
                if index >= 0:
                    self.format_combo.setCurrentIndex(index)

                # 保存重置后的配置
                self.save_config()

                QMessageBox.information(self, "重置成功", "配置已重置为默认值")
                self.logger.info("配置已重置为默认值")

            except Exception as e:
                QMessageBox.critical(self, "重置失败", f"配置重置失败: {str(e)}")
                self.logger.error(f"配置重置失败: {str(e)}")

    def refresh_activation_status(self):
        """刷新激活状态"""
        try:
            # 重新验证激活状态
            is_activated = self.activation_manager.is_activated()
            saved_token = self.activation_manager.load_saved_token()

            if is_activated and saved_token:
                # 更新显示
                masked_token = f"{saved_token[:4]}...{saved_token[-4:]}" if len(saved_token) > 8 else saved_token
                self.token_display.setText(saved_token)
                self.activation_status.setText(f"激活码: {masked_token} | ")
                QMessageBox.information(self, "激活状态", "软件已成功激活")
                self.logger.info("激活状态验证成功")
            else:
                self.token_display.setText("未激活")
                self.activation_status.setText("未激活 | ")
                QMessageBox.warning(self, "激活状态", "软件未激活或激活状态无效，请重新激活")
                self.logger.warning("激活状态验证失败")

            # 更新关于页的激活信息
            self.update_about_activation_info()
        except Exception as e:
            self.logger.error(f"刷新激活状态时发生错误: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"刷新激活状态时发生错误: {str(e)}")

    def browse_config_save_path(self):
        """浏览并选择配置页面的保存路径"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择保存目录", self.config_save_path_input.text()
        )
        if directory:
            self.config_save_path_input.setText(directory)

    def browse_qimao_config_save_path(self):
        """浏览并选择某猫小说保存路径"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择某猫小说保存目录", self.config_qimao_save_path_input.text()
        )
        if directory:
            self.config_qimao_save_path_input.setText(directory)

    def check_update(self):
        """显示更新窗口并检查更新"""
        update_window = UpdateWindow(self)
        update_window.exec_()

    def check_update_silently(self):
        """静默检查更新"""
        # 检查配置中是否开启了自动检查更新
        if not self.config.auto_check_update:
            return

        # 在后台线程中检查更新
        threading.Thread(target=self._check_update_thread, daemon=True).start()

    def _check_update_thread(self):
        """后台检查更新线程"""
        try:
            # 延迟几秒再检查，让主界面先完成加载
            time.sleep(3)

            # 执行检查
            has_update, message, update_info = self.updater.check_update()

            if has_update:
                # 检查是否为强制更新
                is_mandatory = update_info.get("isMandatory", False)

                # 使用Qt元对象系统在主线程中调用UI更新方法
                from PyQt5.QtCore import QMetaObject, Qt, Q_ARG
                if is_mandatory:
                    # 强制更新直接显示更新窗口
                    QMetaObject.invokeMethod(
                        self,
                        "_show_mandatory_update",
                        Qt.QueuedConnection,
                        Q_ARG(dict, update_info),
                        Q_ARG(str, message)
                    )
                else:
                    # 常规更新直接显示更新窗口，而不只是显示通知
                    QMetaObject.invokeMethod(
                        self,
                        "_show_update_window",
                        Qt.QueuedConnection,
                        Q_ARG(dict, update_info),
                        Q_ARG(str, message)
                    )
        except Exception as e:
            self.logger.error(f"静默检查更新失败: {str(e)}")

    @pyqtSlot(dict, str)
    def _show_mandatory_update(self, update_info, message):
        """显示强制更新窗口"""
        version = update_info.get("version", "未知")

        # 显示强制更新提示
        result = QMessageBox.warning(
            self,
            "强制更新",
            f"发现新版本 v{version}，该版本为强制更新，必须更新后才能继续使用。\n请加入QQ群下载最新版本。",
            QMessageBox.Ok
        )

        # 显示更新窗口并禁止关闭
        self.logger.info(f"强制更新模式：显示更新窗口 v{version}")
        update_window = UpdateWindow(self, auto_check=False, allow_close=False)

        # 导入辅助函数
        try:
            from .app import get_update_info_html
            update_window.info_text.setHtml(get_update_info_html(update_info, is_mandatory=True))
        except ImportError:
            # 如果导入失败，使用基本HTML格式化
            info_text = f"<h3>发现新版本: v{version} <span style='color:red;'>(强制更新)</span></h3>\n"
            if "releaseDate" in update_info:
                info_text += f"<p>发布日期: {update_info.get('releaseDate', '未知')}</p>\n"
            info_text += "<p style='color:red; font-weight:bold;'>此版本为强制更新，您必须更新后才能继续使用软件！</p>\n"
            if "changelog" in update_info:
                info_text += f"<h4>更新内容:</h4>\n<p>{update_info.get('changelog', '无')}</p>"
            # 添加QQ群下载提示
            info_text += "<p style='margin-top:15px; font-weight:bold;'>请点击「加入QQ群下载」按钮，在QQ群文件中下载最新版本。</p>"
            update_window.info_text.setHtml(info_text)

        # 启用加入QQ群按钮（注意变量名称已从download_button改为join_qq_button）
        update_window.join_qq_button.setEnabled(True)
        update_window.status_label.setText(message)

        # 执行更新窗口，此处会阻塞直到窗口关闭
        update_result = update_window.exec_()

        # 如果用户以某种方式关闭窗口且未完成更新，则退出应用
        self.logger.info("强制更新窗口已关闭，程序将退出")
        sys.exit(0)

    @pyqtSlot(dict, str)
    def _show_update_window(self, update_info, message):
        """直接显示普通更新窗口(非强制更新)"""
        version = update_info.get("version", "未知")
        self.logger.info(f"显示更新窗口: v{version}")

        # 创建并显示更新窗口
        update_window = UpdateWindow(self, auto_check=False)

        # 导入辅助函数设置更新信息
        try:
            from .app import get_update_info_html
            update_window.info_text.setHtml(get_update_info_html(update_info, is_mandatory=False))
        except ImportError:
            # 如果导入失败，使用基本HTML格式化
            info_text = f"<h3>发现新版本: v{version}</h3>\n"
            if "updateDate" in update_info:
                info_text += f"<p>发布日期: {update_info.get('updateDate', '未知')}</p>\n"
            if "updateDescription" in update_info:
                info_text += f"<h4>更新内容:</h4>\n<p>{update_info.get('updateDescription', '无')}</p>"
            # 添加QQ群下载提示
            info_text += "<p style='margin-top:15px; font-weight:bold;'>请点击「加入QQ群下载」按钮，在QQ群文件中下载最新版本。</p>"
            update_window.info_text.setHtml(info_text)

        update_window.join_qq_button.setEnabled(True)
        update_window.status_label.setText(message)

        # 显示更新窗口
        update_window.exec_()

    def _show_update_notification(self, update_info):
        """显示更新通知 - 保留此方法以兼容旧代码"""
        # 直接使用新方法显示更新窗口
        version = update_info.get("version", "未知")
        message = f"发现新版本: v{version}"
        self._show_update_window(update_info, message)

    def toggle_qimao_input_mode(self):
        """切换某猫小说输入模式"""
        if self.qimao_id_radio.isChecked():
            # ID模式
            self.qimao_id_input.setEnabled(True)
            self.qimao_search_input.setEnabled(False)
            self.qimao_search_btn.setEnabled(False)
            self.qimao_search_result.hide()
        else:
            # 搜索模式
            self.qimao_id_input.setEnabled(False)
            self.qimao_search_input.setEnabled(True)
            self.qimao_search_btn.setEnabled(True)
            if self.qimao_search_result.rowCount() > 0:
                self.qimao_search_result.show()

    def browse_qimao_save_path(self):
        """浏览某猫小说保存路径"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择保存路径", self.qimao_save_path.text()
        )
        if folder:
            self.qimao_save_path.setText(folder)
            # 同时更新配置中的某猫保存路径，并保存配置
            self.config.qimao_save_dir = folder
            # 更新设置页面中的路径输入框
            self.config_qimao_save_path_input.setText(folder)
            try:
                self.config.save()
            except Exception as e:
                self.logger.error(f"保存某猫小说路径配置失败: {str(e)}")

    def search_qimao_novel(self):
        """搜索某猫小说"""
        keyword = self.qimao_search_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, "提示", "请输入搜索关键词")
            return

        self.qimao_status_text.setText("搜索中...")
        self.qimao_search_btn.setEnabled(False)

        # 创建线程进行搜索
        def search_thread():
            from ..book_parser.qimao_processor import QimaoProcessor
            processor = QimaoProcessor()
            try:
                results = processor.search_novel(keyword)
                # 在主线程中更新UI
                QMetaObject.invokeMethod(
                    self,
                    "_show_qimao_search_results",
                    Qt.QueuedConnection,
                    Q_ARG(list, results)
                )
            except Exception as e:
                QMetaObject.invokeMethod(
                    self,
                    "_show_qimao_search_error",
                    Qt.QueuedConnection,
                    Q_ARG(str, str(e))
                )

        # 启动线程
        threading.Thread(target=search_thread, daemon=True).start()

    @pyqtSlot(list)
    def _show_qimao_search_results(self, results):
        """显示某猫小说搜索结果"""
        self.qimao_status_text.setText("空闲")
        self.qimao_search_btn.setEnabled(True)

        # 清空表格
        self.qimao_search_result.setRowCount(0)

        if not results:
            QMessageBox.information(self, "搜索结果", "未找到相关小说")
            return

        # 添加结果到表格
        for idx, book in enumerate(results):
            self.qimao_search_result.insertRow(idx)

            title = book.get("original_title", "未知标题")
            author = book.get("original_author", "未知作者")
            words = book.get("words_num", "未知字数")
            status = "完结" if book.get("is_over") == "1" else "连载"

            # 设置单元格并存储book_id
            title_item = QTableWidgetItem(title)
            title_item.setData(Qt.UserRole, book.get("id", ""))

            self.qimao_search_result.setItem(idx, 0, title_item)
            self.qimao_search_result.setItem(idx, 1, QTableWidgetItem(author))
            self.qimao_search_result.setItem(idx, 2, QTableWidgetItem(words))
            self.qimao_search_result.setItem(idx, 3, QTableWidgetItem(status))

        self.qimao_search_result.show()

    @pyqtSlot(str)
    def _show_qimao_search_error(self, error_msg):
        """显示某猫小说搜索错误"""
        self.qimao_status_text.setText("搜索失败")
        self.qimao_search_btn.setEnabled(True)
        QMessageBox.warning(self, "搜索失败", f"搜索某猫小说失败: {error_msg}")

    def select_qimao_search_result(self, row, column):
        """选择某猫小说搜索结果"""
        book_id = self.qimao_search_result.item(row, 0).data(Qt.UserRole)
        if book_id:
            self.qimao_id_input.setText(book_id)
            self.qimao_id_radio.setChecked(True)

    def start_qimao_download(self):
        """开始下载某猫小说"""
        # 验证激活码是否可用
        token = self.activation_manager.load_saved_token()
        if not token:
            QMessageBox.warning(self, "激活验证", "您尚未激活软件，请先激活后再使用")
            return

        # 检查输入
        if self.qimao_id_radio.isChecked():
            book_id_input = self.qimao_id_input.text().strip()
            if not book_id_input:
                QMessageBox.warning(self, "输入错误", "请输入某猫小说ID或链接")
                return
        else:
            if self.qimao_search_result.rowCount() == 0:
                QMessageBox.warning(self, "输入错误", "请先搜索并选择要下载的小说")
                return

            selected_rows = self.qimao_search_result.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "输入错误", "请选择要下载的小说")
                return

            row = selected_rows[0].row()
            book_id_input = self.qimao_search_result.item(row, 0).data(Qt.UserRole)

        # 检查保存路径
        save_path = self.qimao_save_path.text().strip()
        if not save_path:
            QMessageBox.warning(self, "输入错误", "请选择保存路径")
            return

        # 获取用户选择的格式
        format_choice = "txt" if self.qimao_format_combo.currentIndex() == 0 else "epub"

        # 解析小说ID
        try:
            from ..book_parser.qimao_processor import QimaoProcessor
            processor = QimaoProcessor()
            book_id = processor.parse_book_id(book_id_input)
        except Exception as e:
            QMessageBox.warning(self, "输入错误", str(e))
            return

        # 发起激活码验证请求
        try:
            # 构建请求体
            novel_data = {
                "tokenName": token,
                "novelId": book_id,
                "novelAuthor": "某猫：未知",
                "novelName": "某猫：未知",
                "novelDesc": "某猫：未知",
                "clientVersion": VERSION,
                "formatChoice": format_choice  # 添加格式选择参数
            }
            # 使用当前版本号和小说ID构建请求URL
            verify_url = API_BASE_URL + f"/fq_token/update_num_last"

            # 发送POST请求
            response = requests.post(
                verify_url,
                json=novel_data,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                result = data['code']
                # 处理验证结果
                if result != 200:
                    QMessageBox.critical(self, "服务异常", f"{data['msg']}")
                    return
            else:
                QMessageBox.critical(self, "服务异常", f"{str(response.status_code)}")
                return

            # 其他情况如"200"则继续下载流程，无需提示
        except Exception as e:
            # 遇到网络错误等情况，记录错误但允许继续下载
            self.logger.error(f"请求小说时出错: {str(e)}")

        # 确保保存目录存在
        save_path = os.path.join(save_path, book_id)
        os.makedirs(save_path, exist_ok=True)

        # 禁用下载按钮，启用取消按钮
        self.qimao_download_btn.setEnabled(False)
        self.qimao_cancel_btn.setEnabled(True)

        # 清空之前的信息
        self.qimao_book_name.setText("--")
        self.qimao_book_author.setText("--")
        self.qimao_book_words.setText("--")
        self.qimao_book_tags.setText("--")
        self.qimao_book_intro.setText("")
        self.qimao_result_info.setText("--")
        self.qimao_progress_bar.setValue(0)

        # 创建并启动下载线程
        self.qimao_download_thread = QimaoDownloadWorker(book_id, save_path, format_choice)
        self.qimao_download_thread.update_signal.connect(self.update_qimao_progress)
        self.qimao_download_thread.finished_signal.connect(self.qimao_download_finished)
        self.qimao_download_thread.error_signal.connect(self.qimao_download_error)
        self.qimao_download_thread.start()

    def cancel_qimao_download(self):
        """取消某猫小说下载"""
        if self.qimao_download_thread and self.qimao_download_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "确认取消",
                "确定要取消当前下载吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.qimao_status_text.setText("取消中...")
                self.qimao_download_thread.cancel()

    def clear_qimao_info(self):
        """清空某猫小说下载信息"""
        self.qimao_id_input.clear()
        self.qimao_search_input.clear()
        self.qimao_search_result.setRowCount(0)
        self.qimao_search_result.hide()
        self.qimao_book_name.setText("--")
        self.qimao_book_author.setText("--")
        self.qimao_book_words.setText("--")
        self.qimao_book_tags.setText("--")
        self.qimao_book_intro.setText("")
        self.qimao_result_info.setText("--")
        self.qimao_progress_bar.setValue(0)
        self.qimao_status_text.setText("空闲")
        # 保留保存路径，不清除，因为这是用户配置的保存路径

    def update_qimao_progress(self, data):
        """更新某猫小说下载进度"""
        # 更新状态
        if "status" in data:
            self.qimao_status_text.setText(data["status"])

        # 更新进度条
        if "progress" in data:
            self.qimao_progress_bar.setValue(int(data["progress"]))

        # 更新书籍信息
        if "book_info" in data:
            book_info = data["book_info"]
            self.qimao_book_name.setText(book_info.get("book_name", "--"))
            self.qimao_book_author.setText(book_info.get("author", "--"))
            self.qimao_book_words.setText(book_info.get("words_num", "--"))
            tags = book_info.get("tags", [])
            self.qimao_book_tags.setText("、".join(tags) if tags else "--")
            self.qimao_book_intro.setText(book_info.get("description", ""))

    def qimao_download_finished(self, result):
        """某猫小说下载完成"""
        success = result.get("success", 0)
        failed = result.get("failed", 0)
        book_name = result.get("book_name", "未知书名")
        time_cost = result.get("time_cost", 0)
        merged_path = result.get("merged_path", "")
        epub_path = result.get("epub_path", "")
        from_server = result.get("from_server", False)

        # 更新UI
        self.qimao_status_text.setText("下载完成")
        self.qimao_progress_bar.setValue(100)

        # 显示结果信息
        self.qimao_result_info.setText(
            f"成功: {success}章, 失败: {failed}章, 耗时: {time_cost:.1f}秒"
        )

        # 恢复按钮状态
        self.qimao_download_btn.setEnabled(True)
        self.qimao_cancel_btn.setEnabled(False)

        # 显示完成消息
        message = f"《{book_name}》下载完成!\n\n"
        if from_server:
            message += "从共享服务器下载，无需本地解析\n\n"
        else:
            message += f"成功下载章节: {success}\n"
            message += f"下载失败章节: {failed}\n"
            message += f"耗时: {time_cost:.1f}秒\n\n"

        if merged_path and os.path.exists(merged_path):
            message += f"TXT文件: {merged_path}\n"

        if epub_path and os.path.exists(epub_path):
            message += f"EPUB电子书: {epub_path}\n"

        QMessageBox.information(self, "下载完成", message)

        # 清理线程
        self.qimao_download_thread = None

    def qimao_download_error(self, error_msg):
        """某猫小说下载错误"""
        self.qimao_status_text.setText("下载失败")

        # 恢复按钮状态
        self.qimao_download_btn.setEnabled(True)
        self.qimao_cancel_btn.setEnabled(False)

        # 显示错误消息
        QMessageBox.warning(self, "下载失败", f"某猫小说下载失败: {error_msg}")

        # 清理线程
        self.qimao_download_thread = None

    def closeEvent(self, event):
        """处理窗口关闭事件，确保清理所有资源"""
        try:
            # 停止并清理定时器
            if hasattr(self, 'task_timer') and self.task_timer:
                try:
                    self.task_timer.stop()
                    self.task_timer.deleteLater()
                    self.task_timer = None
                except Exception as e:
                    self.logger.error(f"清理定时器失败: {str(e)}")

            # 取消正在进行的某茄小说下载任务
            if hasattr(self, 'current_novel_id') and self.current_novel_id:
                try:
                    self.task_manager.cancel_task(self.current_novel_id)
                    self.current_novel_id = None
                except Exception as e:
                    self.logger.error(f"取消某茄小说下载任务失败: {str(e)}")

            # 取消正在进行的某猫小说下载任务
            if hasattr(self, 'qimao_download_thread') and self.qimao_download_thread and self.qimao_download_thread.isRunning():
                try:
                    try:
                        self.qimao_download_thread.update_signal.disconnect()
                        self.qimao_download_thread.finished_signal.disconnect()
                        self.qimao_download_thread.error_signal.disconnect()
                    except Exception as e:
                        self.logger.error(f"断开某猫下载线程信号连接失败: {str(e)}")

                    self.qimao_download_thread.cancel()
                    # 设置超时等待，防止无限阻塞
                    self.qimao_download_thread.wait(2000)  # 最多等待2秒

                    # 如果线程仍在运行，则强制终止应用
                    if self.qimao_download_thread.isRunning():
                        self.logger.warning("某猫下载线程未能正常结束，将强制退出")

                    self.qimao_download_thread = None
                except Exception as e:
                    self.logger.error(f"取消某猫小说下载任务失败: {str(e)}")

            # 等待所有后台线程完成
            active_threads = [thread for thread in threading.enumerate()
                              if thread != threading.current_thread() and thread.daemon]

            # 记录活跃线程数量
            if active_threads:
                self.logger.info(f"等待{len(active_threads)}个后台线程完成")

            for thread in active_threads:
                try:
                    thread_name = thread.name
                    self.logger.debug(f"等待线程完成: {thread_name}")
                    thread.join(timeout=1.0)  # 给每个线程最多1秒的时间完成
                    if thread.is_alive():
                        self.logger.warning(f"线程 {thread_name} 未能在超时时间内完成")
                except Exception as e:
                    self.logger.error(f"等待线程完成时出错: {str(e)}")

            event.accept()
        except Exception as e:
            self.logger.error(f"窗口关闭时清理资源失败: {str(e)}")
            event.accept()  # 即使出错也接受关闭事件

    def cancel_download(self):
        """取消下载任务"""
        # 如果有正在进行的任务，取消它
        if self.current_novel_id:
            self.task_manager.cancel_task(self.current_novel_id)
            self.status_label.setText("正在取消任务...")

            # 停止并清理定时器
            if self.task_timer:
                self.task_timer.stop()
                self.task_timer.deleteLater()
                self.task_timer = None

            # 恢复UI状态
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)

            # 重置封面显示
            self.cover_label.setText("暂无封面")
            self.cover_label.setPixmap(QPixmap())

            # 清除当前任务ID
            self.current_novel_id = None

    def clear_download_info(self):
        """清空下载信息"""
        self.book_info_text.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("准备下载...")
        self.cover_label.setText("暂无封面")
        self.cover_label.setPixmap(QPixmap())  # 清除封面图片

    def update_download_progress(self, data):
        """更新下载进度（保留此方法用于向前兼容）"""
        status = data.get("status", "")
        if status:
            self.status_label.setText(status)

        if "progress" in data:
            self.progress_bar.setValue(int(data["progress"]))

        # 更新书籍信息
        if "book_info" in data:
            info = data["book_info"]
            book_name = info.get("book_name", "未知")
            author = info.get("author", "未知")
            tags = info.get("tags", [])
            description = info.get("description", "无简介")
            chapter_count = info.get("chapter_count", 0)

            # 格式化并显示信息
            info_text = f"<h3>{book_name}</h3>\n"
            info_text += f"<p><b>作者:</b> {author}</p>\n"
            if tags and len(tags) > 0:
                info_text += f"<p><b>标签:</b> {', '.join(tags)}</p>\n"
            info_text += f"<p><b>章节数:</b> {chapter_count}</p>\n"
            info_text += f"<p><b>简介:</b><br/>{description}</p>"

            self.book_info_text.setHtml(info_text)

    def download_file(self, url, save_path):
        """从服务器下载文件

        Args:
            url: 文件URL
            save_path: 保存路径
        """
        try:
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 显示下载进度
            self.status_label.setText(f"正在下载文件...")
            self.progress_bar.setValue(0)

            # 创建下载线程
            download_thread = threading.Thread(
                target=self._download_file_thread,
                args=(url, save_path),
                daemon=True
            )
            download_thread.start()

            # 不再等待线程完成，避免UI阻塞
            # 改为使用超时机制
            QTimer.singleShot(60000, lambda: self._check_download_timeout(download_thread))

        except Exception as e:
            self.logger.error(f"启动文件下载线程失败: {str(e)}")
            QMetaObject.invokeMethod(
                self,
                "_download_failed",
                Qt.QueuedConnection,
                Q_ARG(str, str(e))
            )

    def _check_download_timeout(self, thread):
        """检查下载是否超时

        Args:
            thread: 下载线程
        """
        if thread and thread.is_alive():
            self.logger.warning("下载超时，但线程仍在运行")
            QMetaObject.invokeMethod(
                self,
                "_download_failed",
                Qt.QueuedConnection,
                Q_ARG(str, "下载超时，但文件可能仍在后台下载中")
            )

    def _download_file_thread(self, url, save_path):
        """文件下载线程

        Args:
            url: 文件URL
            save_path: 保存路径
        """
        try:
            # 下载文件
            self.logger.info(f"开始从URL下载文件: {url}")

            # 设置超时和重试
            session = requests.Session()
            session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

            response = session.get(url, stream=True, timeout=30)
            response.raise_for_status()  # 检查响应状态

            total_size = int(response.headers.get('content-length', 0))
            block_size = 8192
            downloaded = 0

            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=block_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # 更新进度
                        if total_size > 0:
                            progress = int(downloaded * 100 / total_size)
                            # 使用信号更新UI
                            QMetaObject.invokeMethod(
                                self,
                                "_update_download_progress",
                                Qt.QueuedConnection,
                                Q_ARG(int, progress)
                            )

            self.logger.info(f"文件下载完成: {save_path}")

            # 检查是否需要转换为EPUB
            final_path = save_path
            if hasattr(self, 'need_convert_to_epub') and self.need_convert_to_epub and save_path.lower().endswith('.txt'):
                try:
                    # 在转换为EPUB之前先进行章节校对
                    if (hasattr(self, 'current_enable_proofread') and
                        self.current_enable_proofread and
                        hasattr(self, 'current_chapter_titles') and
                        self.current_chapter_titles):

                        self.logger.info("在转换为EPUB之前先进行章节校对...")
                        from ..book_parser.chapter_proofreader import ChapterProofreader
                        proofreader = ChapterProofreader()
                        proofreader.proofread_novel(save_path, self.current_chapter_titles)

                    # 导入EPUB生成器
                    from ..book_parser.epub_generator import EpubGenerator

                    # 获取书籍信息
                    book_name = os.path.basename(save_path).replace('.txt', '')
                    epub_path = save_path.replace('.txt', '.epub')

                    self.logger.info(f"开始将TXT转换为EPUB: {save_path} -> {epub_path}")

                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(epub_path), exist_ok=True)

                    # 读取TXT内容，尝试多种编码
                    content = None
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']

                    for encoding in encodings:
                        try:
                            with open(save_path, 'r', encoding=encoding) as txt_file:
                                content = txt_file.read()
                            self.logger.info(f"成功使用 {encoding} 编码读取文件")
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            self.logger.warning(f"尝试使用 {encoding} 编码读取文件时出错: {str(e)}")
                            continue

                    # 如果所有编码都失败，使用二进制读取然后尝试强制解码
                    if content is None:
                        self.logger.warning("所有编码都失败，尝试强制解码")
                        with open(save_path, 'rb') as f:
                            raw_content = f.read()
                            # 尝试从二进制数据强制解码
                            content = raw_content.decode('utf-8', errors='replace')

                    # 解析元数据
                    author = "未知作者"
                    description = ""

                    # 从TXT文件开头提取元数据
                    lines = content.split('\n')
                    if len(lines) > 5:
                        for i, line in enumerate(lines[:15]):  # 检查更多行
                            if ("作者" in line or "著" in line) and i < 10:
                                author = line.replace("作者：", "").replace("作者:", "").replace("著：", "").replace("著:", "").strip()
                            if ("简介" in line or "内容简介" in line or "内容提要" in line) and i < 15:
                                desc_start = i
                                # 提取更多行作为描述
                                desc_lines = []
                                for j in range(desc_start, min(desc_start + 10, len(lines))):
                                    if len(lines[j].strip()) > 0 and "章" not in lines[j] and "卷" not in lines[j]:
                                        desc_lines.append(lines[j].strip())
                                    else:
                                        break
                                description = "\n".join(desc_lines)

                    # 创建EPUB转换器，使用正确的参数
                    generator = EpubGenerator(
                        identifier=book_name,  # 使用书名作为标识符
                        title=book_name,
                        language="zh-CN",
                        author=author,
                        description=description,
                        publisher="Useful Novel Tool"
                    )

                    # 生成EPUB
                    success = generator.generate_from_text(content, book_name, author, description, epub_path)

                    if success:
                        self.logger.info(f"EPUB转换完成: {epub_path}")

                        # 删除原始TXT文件
                        try:
                            os.remove(save_path)
                            self.logger.info(f"已删除原始TXT文件: {save_path}")
                        except Exception as e:
                            self.logger.warning(f"删除原始TXT文件失败: {str(e)}")

                        # 更新最终路径
                        final_path = epub_path
                    else:
                        self.logger.error("EPUB转换失败，将使用原始TXT文件")
                        final_path = save_path

                except Exception as e:
                    self.logger.error(f"EPUB转换失败: {str(e)}")
                    # 转换失败时仍使用TXT文件
                    final_path = save_path

            # 下载完成，在主线程中更新UI
            QMetaObject.invokeMethod(
                self,
                "_download_completed",
                Qt.QueuedConnection,
                Q_ARG(str, final_path)
            )

        except Exception as e:
            self.logger.error(f"下载文件线程异常: {str(e)}")
            # 在主线程中显示错误
            QMetaObject.invokeMethod(
                self,
                "_download_failed",
                Qt.QueuedConnection,
                Q_ARG(str, str(e))
            )

    @pyqtSlot(int)
    def _update_download_progress(self, progress):
        """更新下载进度

        Args:
            progress: 进度百分比
        """
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"正在下载文件...{progress}%")

    @pyqtSlot(str)
    def _download_completed(self, file_path):
        """下载完成

        Args:
            file_path: 文件路径
        """
        try:
            self.progress_bar.setValue(100)
            self.status_label.setText(f"文件下载完成")

            # 获取当前小说信息
            novel_id = self.current_novel_id
            task_status = self.task_manager.get_task_status(novel_id)
            if task_status:
                novel_name = task_status.get("novel_name", os.path.basename(file_path))
            else:
                novel_name = os.path.basename(file_path)

            # 构建下载结果
            download_result = {
                "success": 1,
                "failed": 0,
                "canceled": 0,
                "time_cost": 0,
                "book_name": novel_name,
                "merged_path": file_path if file_path.lower().endswith('.txt') else None,
                "epub_path": file_path if file_path.lower().endswith('.epub') else None,
                "from_server": True
            }

            # 使用QTimer延迟显示下载完成消息和触发下载完成回调
            QTimer.singleShot(100, lambda: self._show_download_complete_message(file_path))
            QTimer.singleShot(200, lambda: self.download_finished(download_result))
        except Exception as e:
            self.logger.error(f"处理下载完成回调时出错: {str(e)}")

    @pyqtSlot(str)
    def _download_failed(self, error_message):
        """下载失败

        Args:
            error_message: 错误信息
        """
        try:
            self.progress_bar.setValue(0)
            self.status_label.setText(f"文件下载失败: {error_message}")

            # 使用QTimer延迟显示错误消息
            QTimer.singleShot(100, lambda: QMessageBox.critical(
                self,
                "下载失败",
                f"文件下载失败:\n{error_message}",
                QMessageBox.Ok
            ))

            # 恢复按钮状态
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
        except Exception as e:
            self.logger.error(f"显示下载失败消息时出错: {str(e)}")
            # 确保按钮状态恢复，即使发生错误
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)