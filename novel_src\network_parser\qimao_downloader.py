import os
import time
from PyQt5.QtCore import QThread, pyqtSignal
from typing import Optional

from ..book_parser.qimao_processor import QimaoProcessor
from ..base_system.context import GlobalContext
from .novel_server_api import NovelServerAPI
from ..base_system.activation import ActivationManager


class QimaoDownloadWorker(QThread):
    """七猫小说下载工作线程"""
    update_signal = pyqtSignal(dict)  # 更新进度的信号
    finished_signal = pyqtSignal(dict)  # 完成下载的信号
    error_signal = pyqtSignal(str)  # 错误信号

    def __init__(self, book_id: str, save_path: str, format_choice: str = "txt"):
        super().__init__()
        self.book_id = book_id
        self.save_path = save_path
        self.format_choice = format_choice
        self.logger = GlobalContext.get_logger()
        self.config = GlobalContext.get_config()
        self.is_cancelled = False
        self.processor = None

        # 服务器API
        self.server_api = NovelServerAPI()

        # 激活管理器
        self.activation_manager = ActivationManager()

    def run(self):
        try:
            # 获取用户激活码
            token = self.activation_manager.load_saved_token()
            if not token:
                self.logger.warning("用户未激活，无法与服务器交互")

            # 进度回调函数
            def progress_callback(current, total, message=""):
                if self.is_cancelled:
                    return False

                self.update_signal.emit({
                    "current": current,
                    "total": total,
                    "status": message,
                    "progress": (current / total) * 100 if total > 0 else 0
                })
                return True

            # 初始化七猫处理器
            self.processor = QimaoProcessor(progress_callback=progress_callback)
            self.processor.book_id = self.book_id

            # 通知UI
            self.update_signal.emit({"status": "正在获取七猫小说元数据..."})

            # 获取元数据
            self.processor.get_metadata()

            book_info = {
                "book_name": self.processor.novel_meta["title"],
                "author": self.processor.novel_meta["author"],
                "description": self.processor.novel_meta["intro"],
                "tags": self.processor.novel_meta["tags"],
                "words_num": self.processor.novel_meta["words_num"],
            }

            # 将信息发送到主线程
            self.update_signal.emit({
                "status": "获取书籍信息成功",
                "book_info": book_info
            })

            # 获取章节列表
            self.update_signal.emit({"status": "正在获取章节列表..."})
            self.processor.get_chapters()

            # 显示章节统计
            total_chapters = len(self.processor.catalog)
            self.update_signal.emit({
                "status": f"发现 {total_chapters} 章",
                "total": total_chapters,
            })

            # 执行下载流程
            start_time = time.time()

            # 下载小说
            extract_dir = self.processor.download_novel()
            if not extract_dir:
                self.error_signal.emit("下载失败或已取消")
                return

            # 处理文件
            result = self.processor.process_files(extract_dir, self.save_path, self.format_choice)
            if not result:
                self.error_signal.emit("处理文件失败或已取消")
                return

            # 计算时间
            time_cost = time.time() - start_time

            # 如果下载成功并且用户已激活，尝试上传小说到服务器
            if token and not self.is_cancelled and result.get("merged_path"):
                # 准备上传信息
                novel_info = {
                    "book_name": self.processor.novel_meta["title"],
                    "author": self.processor.novel_meta["author"],
                    "description": self.processor.novel_meta["intro"],
                    "chapter_count": total_chapters
                }

                # 上传小说到服务器但不显示提示信息
                self.server_api.upload_novel(
                    self.book_id,
                    novel_info,
                    result.get("merged_path"),
                    token
                )

            # 下载完成，返回结果
            self.finished_signal.emit({
                "success": result["success"],
                "failed": result["failed"],
                "time_cost": time_cost,
                "book_name": self.processor.novel_meta["title"],
                "merged_path": result["merged_path"],
                "epub_path": result["epub_path"],
                "from_server": False
            })

        except Exception as e:
            self.error_signal.emit(f"下载过程中发生错误: {str(e)}")

    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
        if self.processor:
            self.processor.request_stop() 