# Vue2 某茄小说下载器

一个基于 Vue2 + Element UI 的网页端小说下载器，界面精美，功能完整，与 Python 版本功能对等。

## 🌟 功能特色

### 📚 多平台支持
- **某茄小说**：支持某茄小说平台的小说下载
- **某猫小说**：支持某猫小说平台的小说下载
- **智能解析**：自动解析小说信息和章节列表

### 📖 下载功能
- **多格式支持**：TXT、EPUB 格式导出
- **批量下载**：支持批量选择章节下载
- **智能选择**：全选、反选、范围选择、VIP筛选
- **断点续传**：支持下载中断后继续下载

### 🎯 任务管理
- **任务队列**：智能任务队列管理
- **实时监控**：实时显示下载进度和状态
- **任务控制**：开始、暂停、重试、删除任务
- **历史记录**：完整的下载历史记录

### ⚙️ 个性化设置
- **下载配置**：保存路径、并发数、重试次数等
- **网络设置**：代理配置、User-Agent 设置
- **界面设置**：主题切换、语言选择、字体大小
- **高级选项**：调试模式、缓存管理、日志级别

### 🔐 用户管理
- **激活验证**：基于激活码的用户验证系统
- **机器绑定**：一码一机，保障软件安全
- **试用功能**：支持免费试用功能
- **使用统计**：下载次数统计和限制

### 🎨 界面设计
- **现代化UI**：基于 Element UI 的精美界面
- **响应式设计**：完美适配桌面和移动设备
- **动画效果**：流畅的过渡动画和交互效果
- **主题支持**：浅色/深色主题切换

## 🚀 快速开始

### 环境要求
- Node.js >= 12.0.0
- npm >= 6.0.0 或 yarn >= 1.0.0

### 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发运行
```bash
# 启动开发服务器
npm run serve

# 或使用 yarn
yarn serve
```

访问 http://localhost:8080 查看应用

### 生产构建
```bash
# 构建生产版本
npm run build

# 或使用 yarn
yarn build
```

## 📁 项目结构

```
vue-novel-downloader/
├── public/                 # 静态资源
│   ├── index.html         # HTML 模板
│   └── favicon.ico        # 网站图标
├── src/                   # 源代码
│   ├── api/              # API 接口
│   ├── assets/           # 静态资源
│   ├── components/       # 通用组件
│   ├── router/           # 路由配置
│   ├── store/            # Vuex 状态管理
│   │   └── modules/      # 状态模块
│   ├── styles/           # 样式文件
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   │   ├── Activation/   # 激活页面
│   │   ├── FanqieDownload/ # 某茄下载
│   │   ├── QimaoDownload/  # 某猫下载
│   │   ├── TaskManager/    # 任务管理
│   │   ├── Settings/       # 设置页面
│   │   ├── About/          # 关于页面
│   │   └── Error/          # 错误页面
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── package.json          # 项目配置
├── vue.config.js         # Vue CLI 配置
└── README.md            # 项目说明
```

## 🔧 配置说明

### 环境变量
创建 `.env.local` 文件配置环境变量：

```bash
# API 基础地址
VUE_APP_API_BASE_URL=http://localhost:3000/api

# 应用标题
VUE_APP_TITLE=某茄小说下载器
```

### 代理配置
在 `vue.config.js` 中配置开发服务器代理：

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
```

## 📱 页面说明

### 主界面
- **导航栏**：显示软件标题、版本信息、激活状态
- **侧边栏**：功能模块导航菜单
- **内容区**：各功能页面的主要内容

### 激活页面
- **机器码显示**：自动获取并显示机器码
- **激活码输入**：输入激活码进行软件激活
- **购买引导**：提供购买激活码的渠道
- **试用功能**：支持免费试用

### 下载页面
- **链接解析**：输入小说链接自动解析信息
- **章节选择**：灵活的章节选择功能
- **格式选择**：支持多种下载格式
- **进度显示**：实时显示下载进度

### 任务管理
- **任务列表**：显示所有下载任务
- **状态监控**：实时监控任务状态
- **批量操作**：支持批量管理任务
- **详情查看**：查看任务详细信息

### 设置页面
- **分类设置**：按功能分类的设置选项
- **实时保存**：设置修改后自动保存
- **导入导出**：支持设置的导入导出
- **重置功能**：一键重置为默认设置

## 🛠️ 技术栈

- **前端框架**：Vue 2.6.14
- **UI 组件库**：Element UI 2.15.13
- **状态管理**：Vuex 3.6.2
- **路由管理**：Vue Router 3.5.4
- **HTTP 客户端**：Axios 1.6.0
- **样式预处理**：Sass
- **构建工具**：Vue CLI 5.0.8

## 🎯 开发计划

- [ ] 添加更多小说平台支持
- [ ] 实现云端同步功能
- [ ] 添加阅读器功能
- [ ] 支持自定义主题
- [ ] 添加插件系统
- [ ] 移动端 App 版本

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系我们

- 问题反馈：[GitHub Issues](https://github.com/your-repo/issues)
- 邮箱：<EMAIL>
- QQ群：123456789

## ⭐ 支持项目

如果这个项目对你有帮助，请给它一个 ⭐️！
