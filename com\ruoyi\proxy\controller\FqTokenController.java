package com.ruoyi.proxy.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.proxy.domain.FqToken;
import com.ruoyi.proxy.domain.FqVersion;
import com.ruoyi.proxy.service.FqVersionService;
import com.ruoyi.proxy.service.IFqTokenService;
import com.ruoyi.proxy.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * @Classname TokenController
 * @Date 2024/6/3 17:47
 * @Created by PG_Code
 */
@Slf4j
@RestController
@RequestMapping("/fq_token")
public class FqTokenController extends BaseController {

    @Autowired
    private IFqTokenService tokenService;

    @Autowired
    private FqVersionService versionService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${fq_token.url}")
    private String FANQIE_URL ;

    @Autowired
    private FileUploadUtils fileUploadUtils;

    private final AtomicInteger taskCounter = new AtomicInteger(0);

    private static final String TASK_STATUS_PENDING = "PENDING";
    private static final String TASK_STATUS_PROCESSING = "PROCESSING";
    private static final String TASK_STATUS_COMPLETED = "COMPLETED";
    private static final String TASK_STATUS_FAILED = "FAILED";

    private static final String REDIS_TASK_KEY_PREFIX = "novel_download_task:";
    private static final int TASK_EXPIRE_DAYS = 7;

    /**
     * 查询所有学生
     *
     * @return
     */
    @GetMapping("/selectAll")
    public AjaxResult selectAll() {
        return success(tokenService.selectAll());
    }


    /**
     * 根据名称查询
     *
     * @param tokenName
     * @return
     */
    @GetMapping("/selectByName")
    public String selectByName(@RequestParam String tokenName) {
        String token = tokenService.selectByName(tokenName);
        return token;
    }

    /**
     * 根据id查询
     *
     * @param tokenId
     * @return
     */
    @GetMapping("/selectById")
    public AjaxResult selectById(@RequestParam Integer tokenId) {
        return AjaxResult.success(tokenService.selectById(tokenId));
    }

    /**
     * 分页查询
     *
     * @param tokenName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/selectByPage")
    public AjaxResult selectByPage(@RequestParam String tokenName, @RequestParam Integer tokenId, @RequestParam Integer pageNum, @RequestParam Integer pageSize) {
        return AjaxResult.success(tokenService.selectByPage(tokenName, tokenId, pageNum, pageSize));
    }

    /**
     * 新增Token
     *
     * @param token
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:insert')")
    @PutMapping("/insert")
    public AjaxResult insert(@RequestBody JSONObject token) throws ParseException {

        //检查是否重复获取token
        FqToken fqToken1 = tokenService.selectFqTokenByUserId(getUserId());
        if(fqToken1 != null){
            throw new ServiceException("您已经获取过token了，请勿重复获取");
        }

        FqToken t = new FqToken();
        t.setTokenName(token.getStr("tokenName"));
        if(token.getDate("tokenTime")==null){
            //获取当前时间，时分秒
            Date now = new Date();
            SimpleDateFormat  dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = dateFormat.format(now);
            Date parsedDate = dateFormat.parse(format);
            t.setTokenTime(parsedDate);
        }else {
            t.setTokenTime(token.getDate("tokenTime"));
        }
        t.setUserId(getUserId());
        t.setCreateBy(getUsername());
        return tokenService.insert(t) > 0 ? AjaxResult.success() : AjaxResult.error();
    }


    /**
     * 删除学生
     *
     * @param tokenId
     * @return
     */
    @DeleteMapping("/delete")
    public AjaxResult delete(@RequestParam Integer tokenId) {
        return tokenService.delete(tokenId) > 0 ? AjaxResult.success() : AjaxResult.error();
    }


    /**
     * 修改
     *
     * @param tokenName
     * @return
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestParam String tokenName, @RequestParam String tokenCompCode) {
        return tokenService.update(tokenName, tokenCompCode) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 修改使用次数(old接口防止滥用)
     *
     * @param tokenName 激活码
     * @return
     */
    @PostMapping("/update_num")
    public String updateNumOld(
            @RequestParam String tokenName,
            HttpServletRequest request) {
        // 获取客户端IP地址
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========【{}】开始下载小说，IP地址：{}========>",
                tokenName,  ipAddr);
        return "500";
    }


    /**
     * 修改使用次数
     *
     * @param novelData 小说数据
     * @return 任务ID
     */
    @PostMapping("/update_num_last")
    public AjaxResult updateNum(
            @RequestBody JSONObject novelData,
            HttpServletRequest request) {

        String tokenName = novelData.getStr("tokenName");
        String novelId = novelData.getStr("novelId");
        String novelAuthor = novelData.getStr("novelAuthor");  //作者
        String novelName = novelData.getStr("novelName");  //小说名称
        String novelDesc = novelData.getStr("novelDesc"); //小说描述
        String clientVersion = novelData.getStr("clientVersion");
        JSONArray novelChapterIds = new JSONArray();
        //处理番茄
        if(novelId.length() > 9){
            novelChapterIds = novelData.getJSONArray("novelChapterIds");
        }

        // 获取客户端IP地址
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========【{}】开始下载小说【{}】，客户端版本：{}，IP地址：{}========>",
                tokenName, novelId, clientVersion, ipAddr);

        // IP访问频率检查
        String ipCheckResult = checkIpFrequency(ipAddr);
        if (!"200".equals(ipCheckResult)) {
            log.warn("<==========【{}】下载小说【{}】失败：IP访问频率异常 {}========>", tokenName, novelId, ipAddr);
            return Objects.requireNonNull(AjaxResult.error().put("code", ipCheckResult)).put("msg", "IP访问频率异常");
        }

        //第一版本
        if(clientVersion==null){
            throw new ServiceException("请在群文件下载最新版");
        }

        // 验证客户端版本
        if (!clientVersion.isEmpty()) {
            FqVersion latestVersion = versionService.checkUpdate(clientVersion, "All", "All");
            if (latestVersion != null && latestVersion.getIsMandatory() == 1) {
                // 需要强制更新，不允许使用
                log.warn("<==========【{}】下载小说【{}】失败：客户端版本过低，需要强制更新========>", tokenName, novelId);
                return Objects.requireNonNull(AjaxResult.error().put("code", "510")).put("msg", "客户端版本过低，需要强制更新");
            }
        }

        int i = tokenService.updateNum(tokenName, novelId);

        if (i == 520) {
            log.warn("<==========【{}】下载小说【{}】失败：激活码已禁用========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "520")).put("msg", "激活码已禁用");
        } else if (i == 510) {
            log.warn("<==========【{}】下载小说【{}】失败：激活码未激活========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "510")).put("msg", "激活码未激活");
        } else if (i == 530) {
            log.warn("<==========【{}】下载小说【{}】失败：超过使用额度限制========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "530")).put("msg", "超过使用额度限制");
        } else if (i == 200) {
            // 重复下载，创建任务ID并开始异步下载
            String taskId = createDownloadTask(tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds);
            log.info("<==========【{}】重复下载小说【{}】，不计入次数，任务ID：{}========>", tokenName, novelId, taskId);
            return AjaxResult.success().put("taskId", taskId);
        }

        // 创建任务ID并开始异步下载
        String taskId = createDownloadTask(tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds);
        
        log.info("<==========【{}】下载小说【{}】成功，任务ID：{}========>", tokenName, novelId, taskId);
        return AjaxResult.success().put("taskId", taskId);
    }
    
    /**
     * 创建下载任务并开始异步处理
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param novelChapterIds 章节ID数组
     * @return 任务ID
     */
    private String createDownloadTask(String tokenName, String novelId, String novelName, String novelAuthor, 
                                     String novelDesc, JSONArray novelChapterIds) {
        // 生成唯一任务ID
        String taskId = generateTaskId(tokenName, novelId);
        
        // 创建任务状态信息
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("taskId", taskId);
        taskInfo.put("tokenName", tokenName);
        taskInfo.put("novelId", novelId);
        taskInfo.put("novelName", novelName);
        taskInfo.put("status", TASK_STATUS_PENDING);
        taskInfo.put("progress", 0);
        taskInfo.put("createTime", new Date());
        taskInfo.put("updateTime", new Date());
        
        // 保存任务状态到Redis
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        redisTemplate.opsForHash().putAll(redisKey, taskInfo);
        redisTemplate.expire(redisKey, TASK_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 启动异步任务
        CompletableFuture.runAsync(() -> {
            downloadNovelAsync(taskId, tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds);
        });
        
        return taskId;
    }
    
    /**
     * 生成唯一任务ID
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @return 任务ID
     */
    private String generateTaskId(String tokenName, String novelId) {
        // 生成唯一任务ID: 时间戳_计数器_用户标识_随机数
        return String.format("%d_%d_%s_%s_%d",
                System.currentTimeMillis(),
                taskCounter.incrementAndGet(),
                tokenName.substring(0, Math.min(6, tokenName.length())),
                novelId.substring(0, Math.min(6, novelId.length())),
                new Random().nextInt(1000));
    }
    
    /**
     * 异步下载小说
     * @param taskId 任务ID
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param novelChapterIds 章节ID数组
     */
    @Async
    public void downloadNovelAsync(String taskId, String tokenName, String novelId, String novelName, 
                                  String novelAuthor, String novelDesc, JSONArray novelChapterIds) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        
        try {
            // 更新任务状态为处理中
            updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 10, null, null);
            
            // 处理番茄小说内容
            if (novelId.length() > 9 && novelChapterIds != null && !novelChapterIds.isEmpty()) {
                // 更新任务进度
                updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 20, null, null);
                
                // 下载小说内容
                String novelContent = processFanqieChapters(novelName, novelAuthor, novelDesc, novelChapterIds);
                
                // 更新任务进度
                updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 60, null, null);
                
                if (novelContent != null && !novelContent.isEmpty()) {
                    // 将内容转换为MultipartFile
                    try {
                        // 创建临时文件
                        Path tempFile = Files.createTempFile("novel_" + novelId + "_", ".txt");
                        Files.write(tempFile, novelContent.getBytes("UTF-8"));
                        
                        // 转换为MultipartFile
                        MultipartFile novelFanqieFile = new MockMultipartFile(
                                "novel_" + novelId + ".txt",
                                "novel_" + novelId + ".txt",
                                "text/plain",
                                Files.readAllBytes(tempFile)
                        );
                        
                        // 更新任务进度
                        updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 80, null, null);
                        
                        // 上传到MinIO
                        String fileUrl = fileUploadUtils.uploadMinio(novelFanqieFile, novelId);
                        
                        // 更新任务状态为完成
                        updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, fileUrl, null);
                        
                        log.info("<==========【{}】小说【{}】异步下载完成，文件URL：{}========>", 
                                tokenName, novelId, fileUrl);
                        
                        // 删除临时文件
                        Files.delete(tempFile);
                    } catch (IOException e) {
                        log.error("<==========【{}】小说【{}】创建文件失败：{}========>", 
                                tokenName, novelId, e.getMessage(), e);
                        updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "文件创建失败：" + e.getMessage());
                    }
                } else {
                    log.error("<==========【{}】小说【{}】内容为空========>", tokenName, novelId);
                    updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "小说内容为空");
                }
            } else {
                // 不需要处理章节，直接标记为完成
                updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, null, null);
                log.info("<==========【{}】小说【{}】任务完成，无需下载章节========>", tokenName, novelId);
            }
        } catch (Exception e) {
            log.error("<==========【{}】小说【{}】异步下载失败：{}========>", 
                    tokenName, novelId, e.getMessage(), e);
            updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, e.getMessage());
        }
    }
    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param progress 进度
     * @param fileUrl 文件URL
     * @param errorMsg 错误信息
     */
    private void updateTaskStatus(String taskId, String status, int progress, String fileUrl, String errorMsg) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        
        Map<String, Object> updates = new HashMap<>();
        updates.put("status", status);
        updates.put("progress", progress);
        updates.put("updateTime", new Date());
        
        if (fileUrl != null) {
            updates.put("fileUrl", fileUrl);
        }
        
        if (errorMsg != null) {
            updates.put("errorMsg", errorMsg);
        }
        
        redisTemplate.opsForHash().putAll(redisKey, updates);
        redisTemplate.expire(redisKey, TASK_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 查询任务状态
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/query_task")
    public AjaxResult queryTask(@RequestParam String taskId) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        
        // 检查任务是否存在
        if (!redisTemplate.hasKey(redisKey)) {
            return AjaxResult.error("任务不存在或已过期");
        }
        
        // 获取任务信息
        Map<Object, Object> taskInfo = redisTemplate.opsForHash().entries(redisKey);
        
        return AjaxResult.success(taskInfo);
    }

    /**
     * 处理番茄小说章节内容
     *
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param chapterIds 章节ID数组
     * @return 组装好的小说内容
     */
    private String processFanqieChapters(String novelName, String novelAuthor, String novelDesc, JSONArray chapterIds) {
        StringBuilder novelContent = new StringBuilder();

        // 添加小说头部信息
        novelContent.append("书名：").append(novelName).append("\r\n\r\n");
        novelContent.append("作者：").append(novelAuthor).append("\r\n\r\n");
        novelContent.append("简介：").append(novelDesc).append("\r\n\r\n");
        novelContent.append("====================\r\n\r\n");

        // 处理每个章节
        for (int i = 0; i < chapterIds.size(); i++) {
            String chapterId = chapterIds.getStr(i);
            try {
                // 构建请求URL
                String requestUrl = FANQIE_URL + "?item_id=" + chapterId;

                // 发送HTTP请求获取章节内容
                cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createGet(requestUrl)
                        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                        .execute();

                if (response.isOk()) {
                    String responseBody = response.body();
                    JSONObject jsonObject = new JSONObject(responseBody);
                    JSONObject data = jsonObject.getJSONObject("data");
                    String content = data.getStr("content");
                    System.out.println("请求结果："+ content);

                    // 判断请求是否成功
                    if (!content.isEmpty()) {
                        // 获取章节标题和内容
                        String chapterTitle = data.getStr("title", "第" + (i + 1) + "章");
                        String chapterContent = data.getStr("content", "");

                        // 添加章节标题和内容到小说中
                        novelContent.append(chapterTitle).append("\r\n\r\n");
                        novelContent.append(chapterContent).append("\r\n\r\n");

                        // 添加请求延迟，避免请求过于频繁
                        Thread.sleep(200);
                    } else {
                        log.warn("获取章节内容失败，章节ID：{}，错误信息：{}", chapterId, jsonObject.getStr("message", "未知错误"));
                    }
                } else {
                    log.warn("获取章节内容HTTP请求失败，章节ID：{}，状态码：{}", chapterId, response.getStatus());
                }
            } catch (Exception e) {
                log.error("处理章节内容异常，章节ID：{}，异常信息：{}", chapterId, e.getMessage(), e);
            }
        }

        return novelContent.toString();
    }

    /**
     * 检查IP访问频率
     * 如果5小时内同一IP请求超过30次，则封禁该IP 10小时
     * @param ipAddr IP地址
     * @return 状态码
     */
    private String checkIpFrequency(String ipAddr) {
        // 使用Redis作为存储
        String ipCountKey = "ip_request_count:" + ipAddr;
        String ipBlockKey = "ip_blocked:" + ipAddr;

        // 检查IP是否被封禁
        Boolean isBlocked = redisTemplate.hasKey(ipBlockKey);
        if (isBlocked != null && isBlocked) {
            return "540"; // 自定义错误码：IP被临时封禁
        }

        // 增加IP请求计数
        Long count = redisTemplate.opsForValue().increment(ipCountKey, 1);
        if (count == 1) {
            // 第一次请求，设置5小时过期
            redisTemplate.expire(ipCountKey, 5, TimeUnit.HOURS);
        }

        // 如果5小时内请求次数超过30次，封禁IP 10小时
        if (count > 30) {
            redisTemplate.opsForValue().set(ipBlockKey, "blocked", 10, TimeUnit.HOURS);
            log.warn("检测到IP爬虫行为，IP地址: {}, 已临时封禁10小时", ipAddr);
            return "540"; // IP被临时封禁
        }

        return "200"; // IP检查通过
    }

    /**
     * 修改状态
     *
     * @param tokenName
     * @return
     */
    @PostMapping("/update_status")
    public AjaxResult updateStatus(@RequestParam String tokenName, @RequestParam Integer tokenStatus) {
        return tokenService.updateStatus(tokenName,tokenStatus) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 批量修改状态
     *
     * @param tokens
     * @return
     */
    @PostMapping("/batch_update_status")
    public AjaxResult batchUpdateStatus(@RequestBody JSONObject tokens) {
        JSONArray tokenNames = tokens.getJSONArray("tokenNames");
        Integer tokenStatus = tokens.getInt("tokenStatus");
        return tokenService.updateBatchStatus(tokenNames,tokenStatus) > 0 ? AjaxResult.success(tokenService.updateBatchStatus(tokenNames,tokenStatus)) : AjaxResult.error();
    }

    /**
     * 修改额度
     *
     * @param tokenName
     * @return
     */
    @PostMapping("/update_quota")
    public AjaxResult updateQuota(@RequestParam String tokenName,
                                  @RequestParam Integer baseLimit,
                                  @RequestParam Integer tempNum,
                                  @RequestParam Integer currentUseNum) {
        return tokenService.updateQuota(tokenName,baseLimit,tempNum,currentUseNum) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 获取用户当前额度信息
     * @param tokenName 激活码
     * @param clientVersion 客户端版本
     * @return 额度信息
     */
    @GetMapping("/get_quota")
    public AjaxResult getQuota(
            @RequestParam String tokenName,
            @RequestParam(required = false) String clientVersion,
            HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========用户【{}】查询当前额度信息，客户端版本：{}，IP地址：{}========>",
                tokenName, clientVersion, ipAddr);

        // 验证客户端版本
        if (clientVersion != null && !clientVersion.isEmpty()) {
            FqVersion latestVersion = versionService.checkUpdate(clientVersion, "All", "All");
            if (latestVersion != null && latestVersion.getIsMandatory() == 1) {
                // 需要强制更新，不允许使用
                log.warn("<==========用户【{}】查询额度失败：客户端版本过低，需要强制更新========>", tokenName);
                return AjaxResult.error("客户端版本过低，请更新到最新版本");
            }
        }

        Map<String, Object> quotaInfo = tokenService.getCurrentQuota(tokenName);

        if ("error".equals(quotaInfo.get("status"))) {
            log.warn("<==========用户【{}】查询额度失败：{}========>", tokenName, quotaInfo.get("message"));
            return AjaxResult.error(quotaInfo.get("message").toString());
        }

        log.info("<==========用户【{}】查询额度成功，基础额度：{}，活动额度：{}，总额度：{}，已使用：{}，剩余：{}========>",
                tokenName, quotaInfo.get("baseLimit"), quotaInfo.get("activityLimit"),
                quotaInfo.get("totalLimit"), quotaInfo.get("currentUseNum"), quotaInfo.get("remainingLimit"));

        return AjaxResult.success(quotaInfo);
    }

    /**
     * 一键清空所有用户的当前使用次数
     * @param request 请求对象，用于获取操作者IP
     * @return 清空结果
     */
    @PostMapping("/reset_all_current_use_num")
    public AjaxResult resetAllCurrentUseNum(HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========管理员【{}】执行一键清空所有用户当前使用次数操作========>", ipAddr);

        int count = tokenService.resetAllCurrentUseNum();

        log.info("<==========一键清空所有用户当前使用次数操作完成，共重置{}个用户=========>", count);
        return count > 0 ? AjaxResult.success(count) : AjaxResult.error("没有用户需要重置");
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @DeleteMapping("/delete/batch")
    public AjaxResult deleteByIds(@RequestBody JSONObject ids) {
        List<Integer> list = ids.getJSONArray("ids").toList(Integer.class);
        if (list!=null) {
            for (Integer id : list){
                tokenService.delete(id);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 批量导出数据
     */
    @GetMapping("/export")
    public void exportData(HttpServletResponse response) throws IOException {
        ExcelWriter writer = ExcelUtil.getWriter(true);

        //全部导出
        List<FqToken> list = tokenService.selectAll();

        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList();
        for (FqToken token : list) {
            Map<String, Object> row = new LinkedHashMap<>();

            row.put("序号", token.getTokenId());
            row.put("激活码", token.getTokenName());
            if(token.getTokenCompCode()==null){
                row.put("激活情况", "未激活");
            }else {
                row.put("激活情况", "已激活");
            }
            row.put("创建时间", token.getTokenTime());
            row.put("总使用次数", token.getUseNum());
            row.put("每日使用额度", token.getUseLimit());
            row.put("当前使用次数", token.getCurrentUseNum());

            rows.add(row);
        }


        //设置学号宽度
        writer.getSheet().setColumnWidth(0, 3000);
        //设置激活码宽度
        writer.getSheet().setColumnWidth(1, 12000);
        //设置创建时间宽度
        writer.getSheet().setColumnWidth(3, 6000);

        writer.write(rows, true);


        //设置输出格式
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("激活码信息表", "UTF-8") + ".xlsx");

        ServletOutputStream outputStream = response.getOutputStream();
        writer.flush(outputStream, true);
        writer.close();
        outputStream.flush();
        outputStream.close();
    }

}

/**
 * 模拟MultipartFile实现，用于文件上传
 */
class MockMultipartFile implements MultipartFile {
    private final String name;
    private final String originalFilename;
    private final String contentType;
    private final byte[] content;

    public MockMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.content = content;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public boolean isEmpty() {
        return this.content.length == 0;
    }

    @Override
    public long getSize() {
        return this.content.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return this.content;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(this.content);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        Files.write(dest.toPath(), this.content);
    }
}
