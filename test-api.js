/**
 * 测试解析API功能
 */

const axios = require('axios');

// 测试配置
const PROXY_URL = 'http://localhost:3001';
const TEST_NOVEL_ID = '7512373964816010265';
const TEST_URL = `https://fanqienovel.com/page/${TEST_NOVEL_ID}?enter_from=stack-room`;

// 测试代理服务器健康状态
async function testProxyHealth() {
    console.log('🔍 测试代理服务器健康状态...');
    try {
        const response = await axios.get(`${PROXY_URL}/health`);
        console.log('✅ 代理服务器正常运行');
        console.log('📊 状态信息:', response.data);
        return true;
    } catch (error) {
        console.log('❌ 代理服务器连接失败:', error.message);
        return false;
    }
}

// 测试页面获取功能
async function testFetchPage() {
    console.log('\n🔍 测试页面获取功能...');
    console.log('📄 目标URL:', TEST_URL);

    try {
        const response = await axios.post(`${PROXY_URL}/proxy/fetch-page`, {
            url: TEST_URL
        });

        if (response.data.success) {
            console.log('✅ 页面获取成功');
            console.log('📊 响应状态:', response.data.status);
            console.log('📏 HTML长度:', response.data.html.length, '字符');

            // 简单检查HTML内容
            const html = response.data.html;
            if (html.includes('fanqie') || html.includes('番茄')) {
                console.log('✅ HTML内容验证通过');
                return html;
            } else {
                console.log('⚠️  HTML内容可能不正确');
                return html;
            }
        } else {
            console.log('❌ 页面获取失败:', response.data.error);
            return null;
        }
    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        return null;
    }
}

// 测试HTML解析功能
function testParseHtml(html) {
    console.log('\n🔍 测试HTML解析功能...');

    if (!html) {
        console.log('❌ 没有HTML内容可解析');
        return null;
    }

    try {
        // 使用正则表达式解析HTML（简化版本）
        console.log('📄 使用正则表达式解析HTML内容...');

        // 提取小说名称
        let novelName = '';

        // 尝试从title标签提取
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
            novelName = titleMatch[1]
                .replace(/\s*-\s*番茄小说.*$/, '')
                .replace(/完整版在线免费阅读.*$/, '')
                .replace(/_.*小说.*$/, '')
                .replace(/官网$/, '')
                .trim();
            console.log('📖 从title标签提取标题:', novelName);
        }

        // 尝试从其他可能的位置提取
        if (!novelName) {
            const patterns = [
                /<h1[^>]*data-testid="book-title"[^>]*>([^<]+)<\/h1>/i,
                /<h1[^>]*class="[^"]*book-title[^"]*"[^>]*>([^<]+)<\/h1>/i,
                /《([^》]+)》/
            ];

            for (const pattern of patterns) {
                const match = html.match(pattern);
                if (match) {
                    novelName = match[1].trim();
                    console.log('📖 通过正则表达式找到标题:', novelName);
                    break;
                }
            }
        }

        // 提取作者信息
        let author = '';
        const authorPatterns = [
            /data-testid="book-author"[^>]*>([^<]+)</i,
            /class="[^"]*book-author[^"]*"[^>]*>([^<]+)</i,
            /作者[：:]\s*([^\s\n<]+)/
        ];

        for (const pattern of authorPatterns) {
            const match = html.match(pattern);
            if (match) {
                author = match[1].replace(/^作者[：:]\s*/, '').replace(/\s*著$/, '').trim();
                console.log('👤 找到作者:', author);
                break;
            }
        }

        // 提取简介信息
        let description = '';
        const descPatterns = [
            /data-testid="book-intro"[^>]*>([^<]+)</i,
            /class="[^"]*book-intro[^"]*"[^>]*>([^<]+)</i,
            /class="[^"]*book-description[^"]*"[^>]*>([^<]+)</i
        ];

        for (const pattern of descPatterns) {
            const match = html.match(pattern);
            if (match) {
                description = match[1].trim();
                console.log('📝 找到简介:', description.substring(0, 100) + '...');
                break;
            }
        }

        // 如果没有找到简介，尝试从页面中查找其他描述
        if (!description) {
            const generalDescMatch = html.match(/<p[^>]*>([^<]{50,200})<\/p>/);
            if (generalDescMatch) {
                description = generalDescMatch[1].trim();
                console.log('📝 从段落中提取可能的简介:', description.substring(0, 50) + '...');
            }
        }

        // 使用默认值
        if (!novelName) novelName = `小说_${TEST_NOVEL_ID}`;
        if (!author) author = '未知作者';
        if (!description) description = '暂无简介';

        const bookInfo = {
            novelId: TEST_NOVEL_ID,
            novelName,
            author,
            description,
            parseTime: new Date().toISOString()
        };

        console.log('✅ HTML解析完成');
        console.log('📊 解析结果:', bookInfo);

        return bookInfo;

    } catch (error) {
        console.log('❌ HTML解析失败:', error.message);
        return null;
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始测试书籍信息解析功能\n');

    // 测试1: 代理服务器健康检查
    const proxyHealthy = await testProxyHealth();
    if (!proxyHealthy) {
        console.log('\n❌ 代理服务器不可用，请先启动代理服务器');
        console.log('💡 运行命令: node proxy-server.js');
        return;
    }

    // 测试2: 页面获取
    const html = await testFetchPage();
    if (!html) {
        console.log('\n❌ 页面获取失败，无法继续测试');
        return;
    }

    // 测试3: HTML解析
    const bookInfo = testParseHtml(html);
    if (!bookInfo) {
        console.log('\n❌ HTML解析失败');
        return;
    }

    console.log('\n🎉 所有测试完成！');
    console.log('📋 最终结果:');
    console.log('  📖 书名:', bookInfo.novelName);
    console.log('  👤 作者:', bookInfo.author);
    console.log('  📝 简介:', bookInfo.description.substring(0, 50) + '...');
    console.log('  🕒 解析时间:', new Date(bookInfo.parseTime).toLocaleString());
}

// 不再需要jsdom依赖检查

// 运行测试
runTests().catch(error => {
    console.error('❌ 测试过程中发生错误:', error);
});
