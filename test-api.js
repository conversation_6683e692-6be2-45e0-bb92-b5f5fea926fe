/**
 * 测试解析API功能
 */

const axios = require('axios');

// 测试配置
const PROXY_URL = 'http://localhost:3001';
const TEST_NOVEL_ID = '7512373964816010265';
const TEST_URL = `https://fanqienovel.com/page/${TEST_NOVEL_ID}?enter_from=stack-room`;

// 测试代理服务器健康状态
async function testProxyHealth() {
    console.log('🔍 测试代理服务器健康状态...');
    try {
        const response = await axios.get(`${PROXY_URL}/health`);
        console.log('✅ 代理服务器正常运行');
        console.log('📊 状态信息:', response.data);
        return true;
    } catch (error) {
        console.log('❌ 代理服务器连接失败:', error.message);
        return false;
    }
}

// 测试页面获取功能
async function testFetchPage() {
    console.log('\n🔍 测试页面获取功能...');
    console.log('📄 目标URL:', TEST_URL);

    try {
        const response = await axios.post(`${PROXY_URL}/proxy/fetch-page`, {
            url: TEST_URL
        });

        if (response.data.success) {
            console.log('✅ 页面获取成功');
            console.log('📊 响应状态:', response.data.status);
            console.log('📏 HTML长度:', response.data.html.length, '字符');

            // 简单检查HTML内容
            const html = response.data.html;
            if (html.includes('fanqie') || html.includes('番茄')) {
                console.log('✅ HTML内容验证通过');
                return html;
            } else {
                console.log('⚠️  HTML内容可能不正确');
                return html;
            }
        } else {
            console.log('❌ 页面获取失败:', response.data.error);
            return null;
        }
    } catch (error) {
        console.log('❌ 请求失败:', error.message);
        return null;
    }
}

// 测试HTML解析功能
function testParseHtml(html) {
    console.log('\n🔍 测试HTML解析功能...');

    if (!html) {
        console.log('❌ 没有HTML内容可解析');
        return null;
    }

    try {
        // 使用正则表达式解析HTML（简化版本）
        console.log('📄 使用正则表达式解析HTML内容...');

        // 提取书名 - 完全对应Python版本: title = soup.find("h1").text.strip() if soup.find("h1") else "未知书名"
        let novelName = '未知书名';

        // 精准匹配h1标签
        const h1Match = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
        if (h1Match) {
            const titleText = h1Match[1].trim();
            if (titleText) {
                novelName = titleText;
                console.log('📖 从h1标签提取书名:', novelName);
            }
        }

        if (novelName === '未知书名') {
            console.log('⚠️ 未找到h1标签或内容为空，使用默认书名:', novelName);
        }

        // 提取作者信息 - 完全对应Python版本的author提取逻辑
        let author = '未知作者';

        // 精准匹配div.author-name > span.author-name-text
        const authorMatch = html.match(/<div[^>]*class="[^"]*author-name[^"]*"[^>]*>.*?<span[^>]*class="[^"]*author-name-text[^"]*"[^>]*>([^<]+)<\/span>/s);
        if (authorMatch) {
            const authorText = authorMatch[1].trim();
            if (authorText) {
                author = authorText;
                console.log('👤 从author-name-text提取作者:', author);
            }
        }

        if (author === '未知作者') {
            console.log('⚠️ 未找到div.author-name或span.author-name-text，使用默认作者:', author);
        }

        // 精准提取简介信息 - 完全对应Python版本的description提取逻辑
        let description = '无简介';

        // 精准匹配div.page-abstract-content > p
        const descMatch = html.match(/<div[^>]*class="[^"]*page-abstract-content[^"]*"[^>]*>.*?<p[^>]*>([^<]+)<\/p>/s);
        if (descMatch) {
            const descText = descMatch[1].trim();
            if (descText) {
                description = descText;
                console.log('📝 从page-abstract-content > p提取简介:', description.substring(0, 100) + '...');
            }
        }

        if (description === '无简介') {
            console.log('⚠️ 未找到div.page-abstract-content或其中的p标签，使用默认简介:', description);
        }

        // 提取章节数
        let totalChapters = 0;
        const chapterMatch = html.match(/class="page-directory-header"[^>]*>.*?<h3[^>]*>([^<]*\d+[^<]*)<\/h3>/s);
        if (chapterMatch) {
            const chapterText = chapterMatch[1];
            const numberMatch = chapterText.match(/\d+/);
            if (numberMatch) {
                totalChapters = parseInt(numberMatch[0]);
                console.log('📊 提取章节数:', totalChapters);
            }
        }

        // 最终检查和默认值处理 - 与Python版本保持一致
        if (novelName === '未知书名') {
            console.log('⚠️ 最终书名为默认值:', novelName);
        }
        if (author === '未知作者') {
            console.log('⚠️ 最终作者为默认值:', author);
        }
        if (description === '无简介') {
            console.log('⚠️ 最终简介为默认值:', description);
        }

        const bookInfo = {
            novelId: TEST_NOVEL_ID,
            novelName,
            author,
            description,
            totalChapters,
            parseTime: new Date().toISOString()
        };

        console.log('✅ HTML解析完成');
        console.log('📊 解析结果:', bookInfo);

        return bookInfo;

    } catch (error) {
        console.log('❌ HTML解析失败:', error.message);
        return null;
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始测试书籍信息解析功能\n');

    // 测试1: 代理服务器健康检查
    const proxyHealthy = await testProxyHealth();
    if (!proxyHealthy) {
        console.log('\n❌ 代理服务器不可用，请先启动代理服务器');
        console.log('💡 运行命令: node proxy-server.js');
        return;
    }

    // 测试2: 页面获取
    const html = await testFetchPage();
    if (!html) {
        console.log('\n❌ 页面获取失败，无法继续测试');
        return;
    }

    // 测试3: HTML解析
    const bookInfo = testParseHtml(html);
    if (!bookInfo) {
        console.log('\n❌ HTML解析失败');
        return;
    }

    console.log('\n🎉 所有测试完成！');
    console.log('📋 最终结果:');
    console.log('  📖 书名:', bookInfo.novelName);
    console.log('  👤 作者:', bookInfo.author);
    console.log('  📝 简介:', bookInfo.description.substring(0, 50) + '...');
    console.log('  🕒 解析时间:', new Date(bookInfo.parseTime).toLocaleString());
}

// 不再需要jsdom依赖检查

// 运行测试
runTests().catch(error => {
    console.error('❌ 测试过程中发生错误:', error);
});
