/**
 * 小说相关API统一管理
 * 整合所有小说相关的API接口
 */

import { http } from '@/utils/http'

// 小说API类
class NovelAPI {
  constructor() {
    // this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://toolbox.zjzaki.cn/prod-api'
    this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080'
  }

  /**
   * 从URL提取书籍ID
   * @param {string} url - 小说URL或ID
   * @returns {string|null} 书籍ID
   */
  extractBookId(url) {
    if (!url) return null

    // 检查是否为纯数字ID
    if (/^\d+$/.test(url.trim())) {
      return url.trim()
    }

    // 支持多种URL格式
    const patterns = [
      /\/page\/(\d+)/,           // /page/123456
      /\/reader\/(\d+)/,         // /reader/123456
      /[?&]book_id=(\d+)/,       // ?book_id=123456
      /[?&]bookId=(\d+)/,        // ?bookId=123456
      /(\d{19})/                 // 直接的19位数字
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match && match[1]) {
        return match[1]
      }
    }

    return null
  }

  /**
   * 获取基本书籍信息（不包含章节解析）
   * @param {string} bookId - 书籍ID
   * @returns {Promise<Object>} 书籍信息
   */
  async getBasicBookInfo(bookId) {
    try {
      // 这里可以调用真实的API获取书籍信息
      // 目前返回模拟数据
      return this.getMockBookInfo(bookId)
    } catch (error) {
      console.error('获取基本书籍信息失败:', error)
      return {
        success: false,
        message: error.message || '获取书籍信息失败'
      }
    }
  }

  /**
   * 生成模拟书籍信息
   * @param {string} bookId - 书籍ID
   * @returns {Object} 模拟的书籍信息
   */
  getMockBookInfo(bookId) {
    // 基于书籍ID生成相对固定的模拟数据
    const seed = this.hashCode(bookId)
    const novelTypes = ['玄幻', '都市', '历史', '科幻', '武侠', '言情', '悬疑']
    const novelType = novelTypes[seed % novelTypes.length]

    return {
      success: true,
      data: {
        bookId,
        bookName: `${novelType}小说_${bookId.slice(-6)}`,
        author: this.generateRandomAuthor(seed),
        description: this.generateRandomDescription(novelType, seed),
        coverUrl: require('@/assets/default-cover.png'),
        totalChapters: Math.floor((seed % 200) + 50), // 50-250章
        tags: this.generateRandomTags(novelType, seed)
      }
    }
  }

  /**
   * 生成字符串哈希码
   * @param {string} str - 输入字符串
   * @returns {number} 哈希码
   */
  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 生成随机作者名
   * @param {number} seed - 随机种子
   * @returns {string} 作者名
   */
  generateRandomAuthor(seed) {
    const surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴']
    const names = ['明', '华', '强', '伟', '芳', '娜', '敏', '静', '丽', '勇']

    const surname = surnames[seed % surnames.length]
    const name = names[(seed >> 4) % names.length]
    return surname + name
  }

  /**
   * 生成随机简介
   * @param {string} novelType - 小说类型
   * @param {number} seed - 随机种子
   * @returns {string} 简介
   */
  generateRandomDescription(novelType, seed) {
    const templates = {
      '玄幻': '在这个充满神秘力量的世界里，主角踏上了修炼之路，经历重重考验，最终成为一代强者。',
      '都市': '现代都市中，主角凭借自己的智慧和努力，在商场和情场上都取得了巨大的成功。',
      '历史': '在那个风云变幻的时代，主角运用现代知识，改变了历史的进程。',
      '科幻': '未来世界中，科技高度发达，主角在星际冒险中发现了宇宙的终极秘密。',
      '武侠': '江湖风云，刀光剑影，主角凭借一身武艺，行侠仗义，成为武林传奇。',
      '言情': '一段跨越时空的爱情故事，两个相爱的人经历重重阻碍，最终走到一起。',
      '悬疑': '一个个扑朔迷离的案件，主角运用敏锐的洞察力，揭开了隐藏的真相。'
    }

    return templates[novelType] || '这是一个精彩的故事，讲述了主角的成长历程和冒险经历。'
  }

  /**
   * 生成随机标签
   * @param {string} novelType - 小说类型
   * @param {number} seed - 随机种子
   * @returns {Array<string>} 标签数组
   */
  generateRandomTags(novelType, seed) {
    const tagGroups = {
      '玄幻': ['修炼', '异世界', '系统', '升级', '热血'],
      '都市': ['商战', '豪门', '重生', '都市生活', '现代'],
      '历史': ['穿越', '古代', '权谋', '战争', '历史'],
      '科幻': ['未来', '星际', '机甲', '科技', '太空'],
      '武侠': ['江湖', '武功', '侠客', '门派', '古典'],
      '言情': ['爱情', '甜宠', '现代言情', '古代言情', '浪漫'],
      '悬疑': ['推理', '犯罪', '悬疑', '侦探', '心理']
    }

    const baseTags = tagGroups[novelType] || ['小说', '热门']
    const commonTags = ['完结', '精品', '推荐']

    const selectedTags = []
    selectedTags.push(novelType) // 添加类型标签
    selectedTags.push(baseTags[seed % baseTags.length]) // 添加类型相关标签
    selectedTags.push(commonTags[(seed >> 2) % commonTags.length]) // 添加通用标签

    return selectedTags
  }
}

// 创建实例
const novelAPI = new NovelAPI()

export default novelAPI
