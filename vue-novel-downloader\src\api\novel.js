/**
 * 小说相关API统一管理
 * 整合所有小说相关的API接口
 */

import { http } from '@/utils/http'

// 小说API类
class NovelAPI {
  constructor() {
    // this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://toolbox.zjzaki.cn/prod-api'
    this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080'
  }

  /**
   * 从URL提取书籍ID
   * @param {string} url - 小说URL或ID
   * @returns {string|null} 书籍ID
   */
  extractBookId(url) {
    if (!url) return null

    // 检查是否为纯数字ID
    if (/^\d+$/.test(url.trim())) {
      return url.trim()
    }

    // 支持多种URL格式
    const patterns = [
      /\/page\/(\d+)/,           // /page/123456
      /\/reader\/(\d+)/,         // /reader/123456
      /[?&]book_id=(\d+)/,       // ?book_id=123456
      /[?&]bookId=(\d+)/,        // ?bookId=123456
      /(\d{19})/                 // 直接的19位数字
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match && match[1]) {
        return match[1]
      }
    }

    return null
  }

  /**
   * 解析书籍信息 - 通过访问番茄小说页面获取真实信息
   * @param {string} novelId - 小说ID
   * @returns {Promise<Object>} 书籍信息
   */
  async parseBookInfo(novelId) {
    try {
      if (!novelId) {
        throw new Error('小说ID不能为空')
      }

      // 构建番茄小说页面URL
      const pageUrl = `https://fanqienovel.com/page/${novelId}?enter_from=stack-room`

      // 使用代理服务器获取页面内容（避免CORS问题）
      const response = await this.fetchPageContent(pageUrl)

      if (!response.success) {
        throw new Error(response.message || '获取页面内容失败')
      }

      // 解析HTML内容获取书籍信息
      const bookInfo = this.parseBookInfoFromHtml(response.data, novelId)

      return {
        success: true,
        data: bookInfo
      }
    } catch (error) {
      console.error('解析书籍信息失败:', error)
      return {
        success: false,
        message: error.message || '解析书籍信息失败'
      }
    }
  }

  /**
   * 获取页面内容（通过代理服务器避免CORS）
   * @param {string} url - 页面URL
   * @returns {Promise<Object>} 页面内容
   */
  async fetchPageContent(url) {
    try {
      // 使用本地代理服务器获取页面内容
      const proxyUrl = `http://localhost:3001/proxy/fetch-page`

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ url })
      })

      if (!response.ok) {
        throw new Error(`代理请求失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      // 适配现有代理服务器的响应格式
      if (result.success && result.html) {
        return {
          success: true,
          data: result.html
        }
      } else {
        return {
          success: false,
          message: result.error || '获取页面内容失败'
        }
      }
    } catch (error) {
      console.error('获取页面内容失败:', error)
      // 如果代理服务器不可用，返回模拟数据
      return {
        success: false,
        message: '代理服务器不可用，请确保本地服务器正在运行'
      }
    }
  }

  /**
   * 从HTML内容中解析书籍信息 - 优化版本，精准获取page-abstract-content标签
   * @param {string} html - HTML内容
   * @param {string} novelId - 小说ID
   * @returns {Object} 解析出的书籍信息
   */
  parseBookInfoFromHtml(html, novelId) {
    try {

      // 创建一个临时的DOM解析器
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')

      // 提取书名 - 完全对应Python版本: title = soup.find("h1").text.strip() if soup.find("h1") else "未知书名"
      let novelName = '未知书名'
      const titleElement = doc.querySelector('h1')
      if (titleElement) {
        const titleText = titleElement.textContent.trim()
        if (titleText) {
          novelName = titleText
        }
      }

      // 如果h1标签没有找到有效内容，保持默认值并记录
      if (novelName === '未知书名') {
        console.log(`⚠️ 未找到h1标签或内容为空，使用默认书名: ${novelName}`)
      }

      // 提取作者信息 - 完全对应Python版本的author提取逻辑
      // author = (author_div.find("span", class_="author-name-text").text.strip() if author_div else "未知作者")
      let author = '未知作者'
      const authorDiv = doc.querySelector('div.author-name')
      if (authorDiv) {
        const authorSpan = authorDiv.querySelector('span.author-name-text')
        if (authorSpan) {
          const authorText = authorSpan.textContent.trim()
          if (authorText) {
            author = authorText
          }
        }
      }

      // 如果没有找到，记录并保持默认值
      if (author === '未知作者') {
        console.log(`⚠️ 未找到div.author-name或span.author-name-text，使用默认作者: ${author}`)
      }

      // 精准提取简介信息 - 完全对应Python版本的description提取逻辑
      // description = (desc_div.find("p").text.strip() if desc_div and desc_div.find("p") else "无简介")
      let description = '无简介'
      const descDiv = doc.querySelector('div.page-abstract-content')
      if (descDiv) {
        const descP = descDiv.querySelector('p')
        if (descP) {
          const descText = descP.textContent.trim()
          if (descText) {
            description = descText
          }
        }
      }

      // 如果没有找到，记录并保持默认值
      if (description === '无简介') {
        console.log(`⚠️ 未找到div.page-abstract-content或其中的p标签，使用默认简介: ${description}`)
      }

      // 提取标签信息 - 对应Python版本的tags提取逻辑
      let tags = []
      const tagDiv = doc.querySelector('div.info-label')
      if (tagDiv) {
        const spans = tagDiv.querySelectorAll('span')
        tags = Array.from(spans).map(span => span.textContent.trim()).filter(tag => tag)
      }

      // 提取章节数 - 对应Python版本的chapter_count提取逻辑
      let totalChapters = 0
      const pagesHeaderDiv = doc.querySelector('div.page-directory-header')
      if (pagesHeaderDiv) {
        const h3Element = pagesHeaderDiv.querySelector('h3')
        if (h3Element) {
          const rawText = h3Element.textContent.trim()
          const chapterMatch = rawText.match(/\d+/)
          if (chapterMatch) {
            totalChapters = parseInt(chapterMatch[0])
          }
        }
      }

      // 提取封面图片 - 对应Python版本的image_url提取逻辑
      let coverUrl = ''
      const scriptTags = doc.querySelectorAll('script[type="application/ld+json"]')
      for (const script of scriptTags) {
        try {
          const data = JSON.parse(script.textContent)
          if (data.images && Array.isArray(data.images) && data.images.length > 0) {
            coverUrl = data.images[0]
            break
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }

      // 最终检查和默认值处理 - 与Python版本保持一致
      if (novelName === '未知书名') {
        console.log(`⚠️ 最终书名为默认值: ${novelName}`)
      }
      if (author === '未知作者') {
        console.log(`⚠️ 最终作者为默认值: ${author}`)
      }
      if (description === '无简介') {
        console.log(`⚠️ 最终简介为默认值: ${description}`)
      }

      const bookInfo = {
        novelId,
        novelName,
        author,
        description,
        coverUrl: coverUrl || '',
        totalChapters: totalChapters || 0,
        tags: tags || [],
        parseTime: new Date().toISOString()
      }

      return bookInfo
    } catch (error) {
      console.error('❌ 解析HTML内容失败:', error)
      // 返回基本信息
      return {
        novelId,
        novelName: `小说_${novelId}`,
        author: '未知作者',
        description: '解析失败，暂无简介',
        coverUrl: '',
        totalChapters: 0,
        tags: [],
        parseTime: new Date().toISOString()
      }
    }
  }

  /**
   * 获取基本书籍信息（不包含章节解析）
   * @param {string} bookId - 书籍ID
   * @returns {Promise<Object>} 书籍信息
   */
  async getBasicBookInfo(bookId) {
    try {
      // 优先尝试解析真实信息
      const parseResult = await this.parseBookInfo(bookId)
      if (parseResult.success) {
        return parseResult
      }

      // 如果解析失败，返回模拟数据
      console.warn('解析真实信息失败，使用模拟数据:', parseResult.message)
      return this.getMockBookInfo(bookId)
    } catch (error) {
      console.error('获取基本书籍信息失败:', error)
      return {
        success: false,
        message: error.message || '获取书籍信息失败'
      }
    }
  }

  /**
   * 生成模拟书籍信息
   * @param {string} bookId - 书籍ID
   * @returns {Object} 模拟的书籍信息
   */
  getMockBookInfo(bookId) {
    // 基于书籍ID生成相对固定的模拟数据
    const seed = this.hashCode(bookId)
    const novelTypes = ['玄幻', '都市', '历史', '科幻', '武侠', '言情', '悬疑']
    const novelType = novelTypes[seed % novelTypes.length]

    return {
      success: true,
      data: {
        bookId,
        bookName: `${novelType}小说_${bookId.slice(-6)}`,
        author: this.generateRandomAuthor(seed),
        description: this.generateRandomDescription(novelType, seed),
        coverUrl: require('@/assets/default-cover.png'),
        totalChapters: Math.floor((seed % 200) + 50), // 50-250章
        tags: this.generateRandomTags(novelType, seed)
      }
    }
  }

  /**
   * 生成字符串哈希码
   * @param {string} str - 输入字符串
   * @returns {number} 哈希码
   */
  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 生成随机作者名
   * @param {number} seed - 随机种子
   * @returns {string} 作者名
   */
  generateRandomAuthor(seed) {
    const surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴']
    const names = ['明', '华', '强', '伟', '芳', '娜', '敏', '静', '丽', '勇']

    const surname = surnames[seed % surnames.length]
    const name = names[(seed >> 4) % names.length]
    return surname + name
  }

  /**
   * 生成随机简介
   * @param {string} novelType - 小说类型
   * @param {number} seed - 随机种子
   * @returns {string} 简介
   */
  generateRandomDescription(novelType, seed) {
    const templates = {
      '玄幻': '在这个充满神秘力量的世界里，主角踏上了修炼之路，经历重重考验，最终成为一代强者。',
      '都市': '现代都市中，主角凭借自己的智慧和努力，在商场和情场上都取得了巨大的成功。',
      '历史': '在那个风云变幻的时代，主角运用现代知识，改变了历史的进程。',
      '科幻': '未来世界中，科技高度发达，主角在星际冒险中发现了宇宙的终极秘密。',
      '武侠': '江湖风云，刀光剑影，主角凭借一身武艺，行侠仗义，成为武林传奇。',
      '言情': '一段跨越时空的爱情故事，两个相爱的人经历重重阻碍，最终走到一起。',
      '悬疑': '一个个扑朔迷离的案件，主角运用敏锐的洞察力，揭开了隐藏的真相。'
    }

    return templates[novelType] || '这是一个精彩的故事，讲述了主角的成长历程和冒险经历。'
  }

  /**
   * 生成随机标签
   * @param {string} novelType - 小说类型
   * @param {number} seed - 随机种子
   * @returns {Array<string>} 标签数组
   */
  generateRandomTags(novelType, seed) {
    const tagGroups = {
      '玄幻': ['修炼', '异世界', '系统', '升级', '热血'],
      '都市': ['商战', '豪门', '重生', '都市生活', '现代'],
      '历史': ['穿越', '古代', '权谋', '战争', '历史'],
      '科幻': ['未来', '星际', '机甲', '科技', '太空'],
      '武侠': ['江湖', '武功', '侠客', '门派', '古典'],
      '言情': ['爱情', '甜宠', '现代言情', '古代言情', '浪漫'],
      '悬疑': ['推理', '犯罪', '悬疑', '侦探', '心理']
    }

    const baseTags = tagGroups[novelType] || ['小说', '热门']
    const commonTags = ['完结', '精品', '推荐']

    const selectedTags = []
    selectedTags.push(novelType) // 添加类型标签
    selectedTags.push(baseTags[seed % baseTags.length]) // 添加类型相关标签
    selectedTags.push(commonTags[(seed >> 2) % commonTags.length]) // 添加通用标签

    return selectedTags
  }
}

// 创建实例
const novelAPI = new NovelAPI()

export default novelAPI
