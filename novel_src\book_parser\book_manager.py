# -------------------------------
# book_manager.py - 书籍管理模块
# -------------------------------
import os
import json
import re  # 添加re模块用于处理HTML标签
from pathlib import Path
from typing import Dict

from ..base_system.context import GlobalContext
from .epub_generator import EpubGenerator


class BookManager:
    """书籍文件管理类"""

    def __init__(
            self,
            save_path: str,
            book_id: str,
            book_name: str,
            author: str,
            tags: list,
            description: str,
    ):
        # 书本信息缓存
        self.save_dir = Path(save_path)
        self.book_id = book_id
        self.book_name = book_name
        self.author = author
        self.tags = "|".join(tags)
        self.description = description

        # 初始化
        self.config = GlobalContext.get_config()
        self.logger = GlobalContext.get_logger()

        # 缓存
        self.downloaded: Dict[list] = {}

        # 状态文件路径
        self.status_file = self.config.status_file_path(save_path, book_id)

        self._load_download_status()

    def _load_download_status(self):
        """加载完整的下载状态"""
        try:
            if self.status_file.exists():
                with self.status_file.open("r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.book_name = data.get("book_name", "")
                    self.author = data.get("author", "")
                    self.tags = data.get("tags", "")
                    self.description = data.get("description", "")
                    self.downloaded = data.get("downloaded", {})
        except Exception as e:
            self.logger.error(f"状态文件加载失败: {e}")
            self.downloaded = {}

    def save_download_status(self):
        """保存完整下载状态"""
        if self.downloaded:
            data = {
                "book_name": self.book_name,
                "author": self.author,
                "tags": self.tags,
                "description": self.description,
                "downloaded": self.downloaded,
            }
            try:
                with self.status_file.open("w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                self.logger.error(f"状态文件保存失败: {e}")

    def save_chapter(self, chapter: Dict, title: str, content: str):
        """保存章节内容（统一入口）"""
        self.downloaded[chapter["id"]] = [title, content]
        self.save_download_status()
        self.logger.debug(f"章节 {chapter['id']} 缓存成功")

    def save_error_chapter(self, chapter_id, title):
        """保存下载错误章节"""
        self.downloaded[chapter_id] = [title, "Error"]
        self.save_download_status()
        self.logger.debug(f"章节 {chapter_id} 下载错误记录缓存成功")

    def _convert_html_to_text(self, html_content):
        """将HTML内容转换为纯文本"""
        if not html_content or not isinstance(html_content, str):
            return html_content

        # 替换常见HTML标签
        text = html_content.replace("<p>", "").replace("</p>", "\n")
        text = text.replace("<br>", "\n").replace("<br/>", "\n")

        # 移除其他HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 处理连续空行，将多个连续空行替换为一个空行
        text = re.sub(r'\n{3,}', '\n\n', text)

        # 添加段落缩进（两个全角空格）
        lines = text.split('\n')
        formatted_lines = []

        for line in lines:
            if line.strip():  # 如果行不是空行
                # 为每个非空行添加两个全角空格的缩进
                formatted_lines.append("　　" + line.strip())
            else:
                formatted_lines.append("")

        return "\n".join(formatted_lines)

    def finalize_spawn(self, chapters, result):
        """生成最终文件"""
        output_file = self.save_dir / f"{self.book_name}.{self.config.novel_format}"
        if output_file.exists():
            os.remove(output_file)
        if self.config.novel_format == "epub":
            # 生成EPUB骨架
            epub = EpubGenerator(
                self.book_id,
                self.book_name,
                "zh-CN",
                self.author,
                self.description,
                "番茄小说",
            )

            epub.add_chapter(
                "简介",
                f"<h1>简介</h1><p>{self.tags}</p><p>{self.description}</p>",
                "description.xhtml",
            )

            for chapter in chapters:
                chapter_id = chapter["id"]
                epub.add_chapter(
                    self.downloaded.get(chapter_id, [chapter["title"], None])[0],
                    self.downloaded.get(
                        chapter_id,
                        [None, "<p>Download Faild or Didn't Download Finish!</p>"],
                    )[1],
                )
            epub.generate(output_file)
            self.logger.info(
                f"EPUB生成完成: {self.save_dir / f'{self.book_name}.epub'}"
            )
        else:
            # 移除终端宽度计算，不再需要
            with output_file.open("w", encoding="utf-8") as f:
                # 书籍信息部分
                f.write(f"书名: {self.book_name}\n作者: {self.author}\n标签: {self.tags}\n简介: {self.description}\n\n")

                # 遍历章节内容
                for chapter in chapters:
                    chapter_id = chapter["id"]
                    title = self.downloaded.get(chapter_id, [chapter["title"], None])[0]
                    content = self.downloaded.get(chapter_id, [None, "Download Faild or Didn't Download Finish!"])[1]

                    # 将HTML内容转换为纯文本
                    content = self._convert_html_to_text(content)

                    # 直接使用标题，不进行居中处理
                    # 写入章节标题和内容，标题前后添加空行
                    f.write(f"\n\n{title}\n\n")

                    # 写入章节内容
                    f.write(f"{content}\n")

            self.logger.info(f"TXT生成完成: {output_file}")
        if result == 0 and self.config.auto_clear_dump:
            cover_path = self.save_dir / f"{self.book_name}.jpg"
            if self.status_file.exists():
                os.remove(self.status_file)
                self.logger.debug(f"断点缓存文件已清理！{self.status_file}")
            if cover_path.exists():
                os.remove(cover_path)
                self.logger.debug(f"封面文件已清理！{cover_path}")
