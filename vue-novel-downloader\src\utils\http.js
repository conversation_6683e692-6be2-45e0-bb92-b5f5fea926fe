/**
 * 浏览器端HTTP客户端
 * 使用fetch API，避免axios的Node.js依赖问题
 */

// 基础HTTP客户端类
class HttpClient {
  constructor(baseURL = 'http://toolbox.zjzaki.cn/prod-api', timeout = 30000) {
    this.baseURL = baseURL
    this.timeout = timeout
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    }
  }

  // 设置默认请求头
  setDefaultHeaders(headers) {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers }
  }

  // 创建请求配置
  createRequestConfig(url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`

    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders },
      ...options
    }

    // 合并请求头
    if (options.headers) {
      config.headers = { ...config.headers, ...options.headers }
    }

    // 处理请求体
    if (config.body && typeof config.body === 'object' && config.headers['Content-Type'] === 'application/json') {
      config.body = JSON.stringify(config.body)
    }

    return { url: fullUrl, config }
  }

  // 发送请求
  async request(url, options = {}) {
    const { url: fullUrl, config } = this.createRequestConfig(url, options)

    try {
      // 创建超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)

      config.signal = controller.signal

      const response = await fetch(fullUrl, config)
      clearTimeout(timeoutId)

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 根据Content-Type解析响应
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        return await response.json()
      } else {
        return await response.text()
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('请求超时')
      }
      throw error
    }
  }

  // GET请求
  get(url, params = {}, options = {}) {
    // 处理查询参数
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params)
      url += (url.includes('?') ? '&' : '?') + searchParams.toString()
    }

    return this.request(url, { ...options, method: 'GET' })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: data
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: data
    })
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' })
  }
}

// 创建默认实例
const http = new HttpClient()

// 创建代理客户端实例
const proxyHttp = new HttpClient('http://localhost:3001')

export { HttpClient, http, proxyHttp }
export default http
