<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剩余下载次数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .download-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .download-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .download-info p {
            margin: 8px 0;
        }
        .download-info strong {
            color: #343a40;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>剩余下载次数测试</h1>
        <p>测试 /fq_token/getDownloadCount 接口功能</p>
        
        <div class="input-group">
            <label for="tokenName">激活码 (tokenName)：</label>
            <input type="text" id="tokenName" placeholder="请输入激活码">
        </div>
        
        <div class="input-group">
            <label for="baseUrl">API基础URL：</label>
            <input type="text" id="baseUrl" value="http://localhost:8080" placeholder="例如：http://localhost:8080">
        </div>
        
        <button onclick="getDownloadCount()" id="getBtn">获取剩余下载次数</button>
        <button onclick="testMultipleTokens()" id="testBtn">批量测试</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 获取剩余下载次数
        async function getDownloadCount() {
            const tokenName = document.getElementById('tokenName').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const resultDiv = document.getElementById('result');
            const getBtn = document.getElementById('getBtn');
            
            if (!tokenName) {
                alert('请输入激活码');
                return;
            }
            
            if (!baseUrl) {
                alert('请输入API基础URL');
                return;
            }
            
            try {
                getBtn.disabled = true;
                resultDiv.className = 'result loading';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在获取剩余下载次数，请稍候...';
                
                // 构建请求URL
                const apiUrl = `${baseUrl}/fq_token/getDownloadCount?tokenName=${encodeURIComponent(tokenName)}`;
                console.log('请求URL:', apiUrl);
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                console.log('API响应:', result);
                
                if (response.status === 200) {
                    displayResult(result, tokenName);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>请求失败</h3>
                    <p><strong>错误：</strong>${error.message}</p>
                    <p>请检查：</p>
                    <ul>
                        <li>API服务器是否正在运行</li>
                        <li>URL是否正确</li>
                        <li>网络连接是否正常</li>
                    </ul>
                `;
            } finally {
                getBtn.disabled = false;
            }
        }

        // 显示结果
        function displayResult(result, tokenName) {
            const resultDiv = document.getElementById('result');
            
            let statusBadge = '';
            let statusClass = 'success';
            
            if (result.code === 200 || result.code === '200') {
                statusBadge = '<span class="status-badge status-success">正常</span>';
                statusClass = 'success';
            } else if (result.code === 500 || result.code === '500') {
                statusBadge = '<span class="status-badge status-error">不存在</span>';
                statusClass = 'error';
            } else if (result.code === 520 || result.code === '520') {
                statusBadge = '<span class="status-badge status-warning">已禁用</span>';
                statusClass = 'error';
            } else {
                statusBadge = '<span class="status-badge status-error">未知状态</span>';
                statusClass = 'error';
            }
            
            resultDiv.className = `result ${statusClass}`;
            
            if (result.code === 200 || result.code === '200') {
                const data = result.data || {};
                resultDiv.innerHTML = `
                    <h3>获取成功 ${statusBadge}</h3>
                    <div class="download-info">
                        <h3>下载次数信息</h3>
                        <p><strong>激活码：</strong>${tokenName}</p>
                        <p><strong>剩余下载次数：</strong>${data.remainingDownloads || data.remaining_downloads || 0}</p>
                        <p><strong>总下载次数：</strong>${data.totalDownloads || data.total_downloads || 0}</p>
                        <p><strong>已使用次数：</strong>${data.usedDownloads || data.used_downloads || 0}</p>
                        <p><strong>响应代码：</strong>${result.code}</p>
                        <p><strong>响应消息：</strong>${result.message || result.msg || '成功'}</p>
                        <p><strong>查询时间：</strong>${new Date().toLocaleString()}</p>
                    </div>
                    <details style="margin-top: 10px;">
                        <summary>原始响应数据</summary>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(result, null, 2)}</pre>
                    </details>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h3>获取失败 ${statusBadge}</h3>
                    <p><strong>激活码：</strong>${tokenName}</p>
                    <p><strong>错误代码：</strong>${result.code}</p>
                    <p><strong>错误消息：</strong>${result.message || result.msg || '未知错误'}</p>
                    <p><strong>查询时间：</strong>${new Date().toLocaleString()}</p>
                    <details style="margin-top: 10px;">
                        <summary>原始响应数据</summary>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(result, null, 2)}</pre>
                    </details>
                `;
            }
        }

        // 批量测试多个激活码
        async function testMultipleTokens() {
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');
            
            if (!baseUrl) {
                alert('请输入API基础URL');
                return;
            }
            
            // 测试用的激活码列表（可以根据实际情况修改）
            const testTokens = [
                'test-token-1',
                'test-token-2',
                'invalid-token',
                'disabled-token'
            ];
            
            try {
                testBtn.disabled = true;
                resultDiv.className = 'result loading';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在批量测试激活码，请稍候...';
                
                let results = [];
                
                for (const token of testTokens) {
                    try {
                        const apiUrl = `${baseUrl}/fq_token/getDownloadCount?tokenName=${encodeURIComponent(token)}`;
                        const response = await fetch(apiUrl);
                        const result = await response.json();
                        
                        results.push({
                            token,
                            success: true,
                            data: result
                        });
                    } catch (error) {
                        results.push({
                            token,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                // 显示批量测试结果
                let html = '<h3>批量测试结果</h3>';
                results.forEach((result, index) => {
                    const statusClass = result.success ? 
                        (result.data.code === 200 || result.data.code === '200' ? 'status-success' : 'status-error') : 
                        'status-error';
                    
                    html += `
                        <div class="download-info" style="margin-bottom: 10px;">
                            <p><strong>激活码 ${index + 1}：</strong>${result.token} <span class="status-badge ${statusClass}">${result.success ? (result.data.code || 'OK') : 'ERROR'}</span></p>
                            ${result.success ? 
                                `<p><strong>消息：</strong>${result.data.message || result.data.msg || '成功'}</p>` :
                                `<p><strong>错误：</strong>${result.error}</p>`
                            }
                        </div>
                    `;
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>批量测试失败</h3>
                    <p><strong>错误：</strong>${error.message}</p>
                `;
            } finally {
                testBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
