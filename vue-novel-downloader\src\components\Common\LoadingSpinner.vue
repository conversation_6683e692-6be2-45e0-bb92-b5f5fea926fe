<template>
  <div class="loading-spinner" :class="{ 'fullscreen': fullscreen }">
    <div class="spinner-container">
      <div class="spinner" :style="{ width: size + 'px', height: size + 'px' }">
        <div class="spinner-inner"></div>
      </div>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  
  props: {
    size: {
      type: Number,
      default: 40
    },
    text: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
  }
  
  .spinner-container {
    text-align: center;
    
    .spinner {
      margin: 0 auto;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #409EFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      
      .spinner-inner {
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        border-top: 2px solid #67C23A;
        border-radius: 50%;
        animation: spin 0.8s linear infinite reverse;
      }
    }
    
    .loading-text {
      margin-top: 15px;
      color: #606266;
      font-size: 14px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
