#!/usr/bin/env python3
"""
PyInstaller自动打包脚本
用于将Useful_novel_tool打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path


class NovelToolBuilder:
    """小说工具构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "novel_tool.spec"
        
        # 应用信息
        self.app_name = "Useful_novel_tool"
        self.app_version = "2.1.0"
        self.app_description = "Useful_novel_tool"
        
        # 系统信息
        self.system = platform.system()
        self.is_windows = self.system == "Windows"
        self.is_macos = self.system == "Darwin"
        self.is_linux = self.system == "Linux"
        
    def check_requirements(self):
        """检查构建要求"""
        print("🔍 检查构建环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
            print("   需要Python 3.7或更高版本")
            return False
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("❌ 未安装PyInstaller")
            print("   请运行: pip install pyinstaller")
            return False
        
        # 检查主要依赖
        required_packages = [
            "PyQt5", "requests", "beautifulsoup4", "ebooklib", 
            "pycryptodomex", "tqdm", "fake_useragent"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_").lower())
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package}")
        
        if missing_packages:
            print(f"\n缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        # 检查关键文件
        key_files = [
            "main.py",
            "novel_src/__init__.py",
            "novel_src/gui/app.py",
            "novel_src/gui/main_window.py",
            "novel_src/book_parser/chapter_proofreader.py",
            "requirements.txt"
        ]
        
        for file_path in key_files:
            if not (self.project_root / file_path).exists():
                print(f"❌ 缺少关键文件: {file_path}")
                return False
            print(f"✅ {file_path}")
        
        return True
    
    def clean_build_dirs(self):
        """清理构建目录"""
        print("\n🧹 清理构建目录...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ 已清理: {dir_path}")
        
        # 删除旧的spec文件
        if self.spec_file.exists():
            os.remove(self.spec_file)
            print(f"✅ 已删除旧的spec文件: {self.spec_file}")
    
    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        print("\n📝 创建PyInstaller配置文件...")
        
        # 图标文件路径
        icon_path = self.project_root / "img" / "logo.png"
        if self.is_windows:
            icon_path = self.project_root / "img" / "log_ico.ico"
        
        icon_option = f"icon='{icon_path}'" if icon_path.exists() else "icon=None"
        
        # 数据文件
        datas = [
            "('img', 'img')",
            "('config.yml', '.')",
        ]
        
        # 隐藏导入
        hidden_imports = [
            "'PyQt5.QtCore'",
            "'PyQt5.QtGui'", 
            "'PyQt5.QtWidgets'",
            "'requests'",
            "'beautifulsoup4'",
            "'ebooklib'",
            "'pycryptodomex'",
            "'tqdm'",
            "'fake_useragent'",
            "'novel_src.book_parser.chapter_proofreader'",
            "'novel_src.gui.main_window'",
            "'novel_src.network_parser.downloader'",
            "'novel_src.book_parser.book_manager'",
        ]
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[{', '.join(datas)}],
    hiddenimports=[{', '.join(hidden_imports)}],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {icon_option},
    version_file=None,
)
'''
        
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ 已创建spec文件: {self.spec_file}")
    
    def build_executable(self):
        """构建可执行文件"""
        print("\n🔨 开始构建可执行文件...")
        
        # PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            str(self.spec_file)
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ 构建成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败！")
            print(f"错误代码: {e.returncode}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def post_build_tasks(self):
        """构建后任务"""
        print("\n📦 执行构建后任务...")
        
        # 查找生成的可执行文件
        exe_name = f"{self.app_name}.exe" if self.is_windows else self.app_name
        exe_path = self.dist_dir / exe_name
        
        if not exe_path.exists():
            print(f"❌ 未找到可执行文件: {exe_path}")
            return False
        
        # 显示文件信息
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ 可执行文件: {exe_path}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        
        # 创建发布目录
        release_dir = self.project_root / "release"
        release_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件到发布目录
        release_exe = release_dir / exe_name
        shutil.copy2(exe_path, release_exe)
        print(f"✅ 已复制到发布目录: {release_exe}")
        
        # 创建README文件
        readme_content = f"""# {self.app_name} v{self.app_version}

{self.app_description}

## 新功能
- ✅ 章节顺序校对功能
- ✅ 智能章节匹配和重排序
- ✅ 自动备份原文件
- ✅ 详细的校对进度提示

## 使用方法
1. 运行 {exe_name}
2. 在Useful_novel_tool界面输入小说ID或链接
3. 勾选"启用章节顺序校对"选项（默认已启用）
4. 选择保存路径和格式（推荐TXT格式以使用校对功能）
5. 点击"开始下载"

## 注意事项
- 章节校对功能仅支持TXT格式
- 校对过程中会自动创建备份文件（.backup）
- 如有问题可通过备份文件恢复原始内容

## 系统要求
- Windows 7/10/11 (64位)
- 网络连接

构建时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
构建系统: {platform.system()} {platform.release()}
"""
        
        readme_path = release_dir / "README.txt"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 已创建说明文件: {readme_path}")
        
        return True
    
    def build(self):
        """执行完整构建流程"""
        print(f"🚀 开始构建 {self.app_name} v{self.app_version}")
        print(f"🖥️  目标系统: {self.system}")
        print("=" * 50)
        
        # 检查构建要求
        if not self.check_requirements():
            print("\n❌ 构建要求检查失败，请解决上述问题后重试")
            return False
        
        # 清理构建目录
        self.clean_build_dirs()
        
        # 创建spec文件
        self.create_spec_file()
        
        # 构建可执行文件
        if not self.build_executable():
            print("\n❌ 构建失败")
            return False
        
        # 构建后任务
        if not self.post_build_tasks():
            print("\n❌ 构建后任务失败")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 构建完成！")
        print(f"📁 发布文件位于: {self.project_root / 'release'}")
        return True


def main():
    """主函数"""
    builder = NovelToolBuilder()
    
    try:
        success = builder.build()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程中发生未预期的错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
