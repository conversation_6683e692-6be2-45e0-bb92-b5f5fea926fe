# Vue下载器与Python API集成说明

## 概述

本次修改将Vue下载器的小说下载逻辑和任务进度查询逻辑与Python版本的`novel_server_api.py`文件保持一致，确保前后端API接口的兼容性。

## 主要修改内容

### 1. 下载Store模块 (`src/store/modules/download.js`)

#### 新增状态字段
- `taskId`: 服务器返回的任务ID
- `status`: 任务状态 (pending/processing/completed/failed)
- `fileUrl`: 下载完成后的文件URL
- `errorMessage`: 错误信息
- `downloadCompleted`: 下载完成标志

#### 修改的Actions
- **`startDownload`**: 基于Python版本的`request_novel_download`方法
  - 调用`/fq_token/update_num_last`接口
  - 支持番茄小说章节ID和标题传递
  - 返回任务ID用于后续状态查询

- **`pollDownloadProgress`**: 基于Python版本的`check_download_task`方法
  - 调用`/fq_token/query_task`接口
  - 状态映射与Python版本保持一致
  - 支持下载完成后的文件URL获取

#### 新增Actions
- **`downloadFile`**: 基于Python版本的`download_novel_file`方法
- **`checkUserQuota`**: 基于Python版本的`get_user_quota`方法

### 2. 用户Store模块 (`src/store/modules/user.js`)

#### 新增状态字段
- `quotaInfo`: 用户额度信息
  - `totalQuota`: 总额度
  - `usedQuota`: 已使用额度
  - `remainingQuota`: 剩余额度
  - `lastUpdated`: 最后更新时间

#### 新增Actions
- **`getUserQuota`**: 获取用户额度信息

### 3. Vue组件 (`src/views/FanqieDownload/index.vue`)

#### UI增强
- 下载完成提示框，显示成功信息和操作按钮
- 下载失败提示框，显示错误信息和重试按钮
- 文件下载按钮和链接复制功能

#### 新增方法
- **`downloadCompletedFile`**: 下载完成后的文件下载
- **`copyDownloadLink`**: 复制下载链接到剪贴板
- **`retryDownload`**: 重试下载
- **`checkQuota`**: 检查用户额度

### 4. API接口 (`src/api/index.js`)

#### 重构下载相关API
- **`requestNovelDownload`**: 对应Python的`request_novel_download`
- **`checkDownloadTask`**: 对应Python的`check_download_task`
- **`getUserQuota`**: 对应Python的`get_user_quota`

## API接口映射

| Python方法 | Vue API | 接口地址 | 说明 |
|------------|---------|----------|------|
| `request_novel_download` | `requestNovelDownload` | `/fq_token/update_num_last` | 请求下载小说 |
| `check_download_task` | `checkDownloadTask` | `/fq_token/query_task` | 查询任务状态 |
| `get_user_quota` | `getUserQuota` | `/fq_token/get_quota` | 获取用户额度 |

## 状态映射

| 服务器状态 | Vue状态 | 说明 |
|-----------|---------|------|
| PENDING | pending | 等待中 |
| PROCESSING | processing | 处理中 |
| COMPLETED | completed | 已完成 |
| FAILED | failed | 失败 |

## 使用方式

### 1. 启动下载
```javascript
// 在Vue组件中
await this.startDownloadAction()
```

### 2. 查询用户额度
```javascript
// 在Vue组件中
const quota = await this.checkQuota()
```

### 3. 下载完成后的文件
```javascript
// 在Vue组件中
await this.downloadCompletedFile()
```

## 测试工具

提供了API测试工具 (`src/utils/test-api.js`)，可以在浏览器控制台中测试API兼容性：

```javascript
// 在浏览器控制台中运行
window.testAPI.runAllTests()
```

## 配置说明

### API基础地址
- 生产环境: `http://toolbox.zjzaki.cn/prod-api`
- 开发环境: 可在`vue.config.js`中配置代理

### 客户端版本
- 当前版本: `2.2.0`
- 与Python版本保持一致

## 注意事项

1. **网络请求**: 所有API请求都使用原生`fetch`，避免依赖问题
2. **错误处理**: 完善的错误处理和用户提示
3. **状态同步**: 前端状态与后端API响应保持同步
4. **兼容性**: 保持与Python版本API的完全兼容

## 后续优化建议

1. 添加下载进度的实时更新
2. 支持批量下载任务管理
3. 添加下载历史记录功能
4. 优化错误重试机制
5. 添加下载速度限制功能

## 测试清单

- [ ] 激活码验证
- [ ] 小说信息解析
- [ ] 下载任务创建
- [ ] 任务状态查询
- [ ] 文件下载功能
- [ ] 用户额度查询
- [ ] 错误处理机制
- [ ] UI交互体验
