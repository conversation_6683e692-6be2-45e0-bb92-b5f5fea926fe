# 部署指南

本文档介绍如何部署 Vue2 某茄小说下载器到不同的环境。

## 📦 构建生产版本

### 1. 安装依赖
```bash
npm install
# 或
yarn install
```

### 2. 构建项目
```bash
npm run build
# 或
yarn build
```

构建完成后，会在 `dist` 目录生成静态文件。

## 🌐 部署到静态服务器

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API 代理
    location /api/ {
        proxy_pass http://your-backend-server:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### Apache 配置示例

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/dist

    # 处理 Vue Router 的 history 模式
    <Directory "/path/to/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## ☁️ 部署到云平台

### Vercel 部署

1. 在项目根目录创建 `vercel.json`：

```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

2. 部署命令：
```bash
npm install -g vercel
vercel --prod
```

### Netlify 部署

1. 在项目根目录创建 `_redirects` 文件：
```
/*    /index.html   200
```

2. 在 `netlify.toml` 中配置：
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### GitHub Pages 部署

1. 安装 gh-pages：
```bash
npm install --save-dev gh-pages
```

2. 在 `package.json` 中添加部署脚本：
```json
{
  "scripts": {
    "deploy": "gh-pages -d dist"
  }
}
```

3. 部署：
```bash
npm run build
npm run deploy
```

## 🐳 Docker 部署

### Dockerfile

```dockerfile
# 构建阶段
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml

```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  # 如果有后端服务
  api:
    image: your-backend-image
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

### 构建和运行

```bash
# 构建镜像
docker build -t novel-downloader .

# 运行容器
docker run -d -p 80:80 novel-downloader

# 或使用 docker-compose
docker-compose up -d
```

## 🔧 环境配置

### 生产环境变量

创建 `.env.production` 文件：

```bash
# API 基础地址
VUE_APP_API_BASE_URL=https://api.your-domain.com

# 应用标题
VUE_APP_TITLE=某茄小说下载器

# 启用生产模式
NODE_ENV=production

# 禁用调试
VUE_APP_DEBUG=false
```

### 测试环境变量

创建 `.env.staging` 文件：

```bash
# API 基础地址
VUE_APP_API_BASE_URL=https://api-staging.your-domain.com

# 应用标题
VUE_APP_TITLE=某茄小说下载器 (测试版)

# 启用调试
VUE_APP_DEBUG=true
```

## 📊 性能优化

### 1. 代码分割

在 `vue.config.js` 中配置：

```javascript
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          elementUI: {
            name: 'chunk-elementUI',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/
          }
        }
      }
    }
  }
}
```

### 2. 启用 PWA

```bash
vue add @vue/pwa
```

### 3. 预加载关键资源

在 `public/index.html` 中添加：

```html
<link rel="preload" href="/static/css/app.css" as="style">
<link rel="preload" href="/static/js/app.js" as="script">
```

## 🔍 监控和日志

### 1. 错误监控

集成 Sentry：

```bash
npm install @sentry/vue @sentry/tracing
```

在 `main.js` 中配置：

```javascript
import * as Sentry from "@sentry/vue"
import { Integrations } from "@sentry/tracing"

Sentry.init({
  Vue,
  dsn: "YOUR_SENTRY_DSN",
  integrations: [
    new Integrations.BrowserTracing(),
  ],
  tracesSampleRate: 1.0,
})
```

### 2. 访问统计

集成 Google Analytics：

```html
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

## 🔒 安全配置

### 1. HTTPS 配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
}
```

### 2. CSP 配置

在 `public/index.html` 中添加：

```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;">
```

## 🚀 CI/CD 配置

### GitHub Actions 示例

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.4
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /path/to/project
          git pull origin main
          npm ci
          npm run build
          sudo systemctl reload nginx
```

## 📋 部署检查清单

- [ ] 环境变量配置正确
- [ ] API 接口地址配置正确
- [ ] 静态资源路径配置正确
- [ ] 路由模式配置正确
- [ ] 缓存策略配置
- [ ] HTTPS 证书配置
- [ ] 安全头配置
- [ ] 错误监控配置
- [ ] 性能监控配置
- [ ] 备份策略制定
- [ ] 回滚方案准备

## 🆘 故障排除

### 常见问题

1. **页面刷新 404**
   - 检查服务器是否配置了 history 模式的重写规则

2. **静态资源加载失败**
   - 检查 `publicPath` 配置
   - 检查服务器静态资源路径

3. **API 请求失败**
   - 检查 API 基础地址配置
   - 检查跨域配置

4. **白屏问题**
   - 检查控制台错误信息
   - 检查资源加载情况

### 日志查看

```bash
# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Docker 日志
docker logs container-name

# PM2 日志
pm2 logs
```
