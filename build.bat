@echo off
chcp 65001 >nul
title 某茄小说下载器 - 自动打包工具

echo.
echo ========================================
echo    某茄小说下载器 - 自动打包工具
echo ========================================
echo.

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:: 显示选项
echo 请选择构建模式:
echo [1] 快速构建 (开发测试用，单文件)
echo [2] 完整构建 (发布用，包含所有优化)
echo [3] 安装依赖包
echo [4] 退出
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto quick_build
if "%choice%"=="2" goto full_build  
if "%choice%"=="3" goto install_deps
if "%choice%"=="4" goto exit
goto invalid_choice

:quick_build
echo.
echo 🚀 开始快速构建...
python quick_build.py
goto end

:full_build
echo.
echo 🔨 开始完整构建...
python build_exe.py
goto end

:install_deps
echo.
echo 📦 安装依赖包...
echo 正在安装PyInstaller...
pip install pyinstaller
echo.
echo 正在安装项目依赖...
pip install -r requirements.txt
echo.
echo ✅ 依赖包安装完成
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请输入1-4
echo.
goto end

:exit
echo.
echo 👋 再见！
exit /b 0

:end
echo.
pause
