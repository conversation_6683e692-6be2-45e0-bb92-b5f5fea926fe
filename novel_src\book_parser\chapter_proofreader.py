"""
章节校对器模块
用于校对下载的TXT小说文件中的章节顺序
"""

import re
import os
import logging
from typing import List, Dict, Tuple, Optional
from difflib import SequenceMatcher


class ChapterProofreader:
    """章节校对器类，用于校对小说章节顺序"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def proofread_novel(self, txt_file_path: str, expected_chapter_titles: List[str]) -> bool:
        """
        校对小说章节顺序
        
        Args:
            txt_file_path: TXT文件路径
            expected_chapter_titles: 期望的章节标题列表（按正确顺序）
            
        Returns:
            bool: 校对是否成功
        """
        try:
            if not os.path.exists(txt_file_path):
                self.logger.error(f"文件不存在: {txt_file_path}")
                return False
                
            if not expected_chapter_titles:
                self.logger.warning("章节标题列表为空，跳过校对")
                return True
                
            self.logger.info(f"📚 开始校对小说章节顺序: {txt_file_path}")
            self.logger.info(f"📊 期望章节数量: {len(expected_chapter_titles)}")

            # 读取文件内容
            self.logger.info("📖 正在读取文件内容...")
            content = self._read_file_with_encoding(txt_file_path)
            if not content:
                self.logger.error("❌ 无法读取文件内容")
                return False

            # 解析文件中的章节
            self.logger.info("🔍 正在解析章节结构...")
            parsed_chapters = self._parse_chapters_from_content(content)
            if not parsed_chapters:
                self.logger.warning("⚠️ 未能从文件中解析出章节，跳过校对")
                return True

            self.logger.info(f"✅ 从文件中解析出 {len(parsed_chapters)} 个章节")

            # 匹配和重排章节
            self.logger.info("🔄 正在匹配和重排章节...")
            reordered_chapters = self._match_and_reorder_chapters(parsed_chapters, expected_chapter_titles)

            if not reordered_chapters:
                self.logger.warning("⚠️ 无法重新排序章节，保持原有顺序")
                return True
                
            # 生成校对后的内容
            self.logger.info("📝 正在生成校对后的内容...")
            corrected_content = self._generate_corrected_content(content, reordered_chapters)

            # 备份原文件
            backup_path = txt_file_path + ".backup"
            try:
                self.logger.info("💾 正在创建备份文件...")
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.logger.info(f"✅ 已创建备份文件: {backup_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ 创建备份文件失败: {str(e)}")

            # 写入校对后的内容
            self.logger.info("💾 正在保存校对后的文件...")
            with open(txt_file_path, 'w', encoding='utf-8') as f:
                f.write(corrected_content)

            self.logger.info("🎉 章节校对完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"章节校对失败: {str(e)}")
            return False
    
    def _read_file_with_encoding(self, file_path: str) -> Optional[str]:
        """尝试多种编码读取文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                self.logger.debug(f"成功使用 {encoding} 编码读取文件")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.logger.warning(f"使用 {encoding} 编码读取文件时出错: {str(e)}")
                continue
                
        self.logger.error("无法使用任何编码读取文件")
        return None
    
    def _parse_chapters_from_content(self, content: str) -> List[Dict[str, str]]:
        """从内容中解析章节"""
        chapters = []
        lines = content.split('\n')

        # 跳过书籍信息部分
        start_index = 0
        for i, line in enumerate(lines):
            if "===========" in line:
                start_index = i + 1
                break

        # 章节标题匹配模式（按优先级排序）
        chapter_patterns = [
            r'^第\s*\d+\s*章.*',  # 第1章、第 2 章等
            r'^第[零一二三四五六七八九十百千万]+章.*',  # 第一章、第二章等
            r'^\d+[\s\.\-、:：]+.*',  # 1. 、2、等
            r'^第[零一二三四五六七八九十百千万]+[卷篇部集].*',  # 第一卷等
        ]

        current_title = None
        current_content = []

        for line_num, line in enumerate(lines[start_index:], start_index):
            line = line.strip()

            # 检查是否是章节标题
            is_chapter_title = False
            for pattern in chapter_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    # 额外检查：标题长度不应过长，且包含"章"字
                    if len(line) < 100 and "章" in line:
                        is_chapter_title = True
                        self.logger.debug(f"找到章节标题 (行{line_num}): {line}")
                        break

            if is_chapter_title:
                # 保存前一章节
                if current_title and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if content_text:  # 只保存有内容的章节
                        chapters.append({
                            'title': current_title,
                            'content': content_text
                        })
                        self.logger.debug(f"保存章节: {current_title} (内容长度: {len(content_text)})")

                # 开始新章节
                current_title = line
                current_content = []
            else:
                # 收集章节内容
                if line:  # 只添加非空行
                    current_content.append(line)
                elif current_content:  # 保留段落间的空行
                    current_content.append('')

        # 添加最后一章
        if current_title and current_content:
            content_text = '\n'.join(current_content).strip()
            if content_text:
                chapters.append({
                    'title': current_title,
                    'content': content_text
                })
                self.logger.debug(f"保存最后章节: {current_title} (内容长度: {len(content_text)})")

        self.logger.info(f"解析完成，找到 {len(chapters)} 个章节")
        for i, chapter in enumerate(chapters):
            self.logger.debug(f"章节 {i+1}: {chapter['title']}")

        return chapters
    
    def _match_and_reorder_chapters(self, parsed_chapters: List[Dict[str, str]],
                                   expected_titles: List[str]) -> List[Dict[str, str]]:
        """匹配并重新排序章节"""
        if not parsed_chapters or not expected_titles:
            return parsed_chapters

        # 创建标题到章节的映射
        title_to_chapter = {}
        for chapter in parsed_chapters:
            title_to_chapter[chapter['title']] = chapter

        reordered = []
        matched_count = 0
        used_chapters = set()

        self.logger.info(f"开始匹配章节，期望 {len(expected_titles)} 个，解析到 {len(parsed_chapters)} 个")

        for i, expected_title in enumerate(expected_titles):
            best_match = None
            best_score = 0

            self.logger.debug(f"匹配期望标题 {i+1}: {expected_title}")

            # 寻找最佳匹配
            for parsed_title in title_to_chapter.keys():
                if parsed_title in used_chapters:
                    continue

                # 计算相似度
                score = self._calculate_title_similarity(expected_title, parsed_title)
                self.logger.debug(f"  与 '{parsed_title}' 的相似度: {score:.3f}")

                if score > best_score and score > 0.3:  # 降低相似度阈值
                    best_score = score
                    best_match = parsed_title

            if best_match:
                chapter = title_to_chapter[best_match].copy()  # 复制章节
                # 更新标题为期望的标题
                chapter['title'] = expected_title
                reordered.append(chapter)
                matched_count += 1
                used_chapters.add(best_match)
                self.logger.info(f"✓ 匹配成功: '{expected_title}' <- '{best_match}' (相似度: {best_score:.3f})")
            else:
                self.logger.warning(f"✗ 未找到匹配: '{expected_title}'")

        # 添加未匹配的章节到末尾
        for parsed_title, chapter in title_to_chapter.items():
            if parsed_title not in used_chapters:
                reordered.append(chapter)
                self.logger.info(f"+ 添加未匹配章节: '{parsed_title}'")

        self.logger.info(f"匹配完成: {matched_count}/{len(expected_titles)} 个章节成功匹配")

        return reordered
    
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """计算两个标题的相似度"""
        # 清理标题（移除空格、标点等，保留中文和数字）
        clean_title1 = re.sub(r'[^\w\u4e00-\u9fff]', '', title1.lower())
        clean_title2 = re.sub(r'[^\w\u4e00-\u9fff]', '', title2.lower())

        # 如果标题完全相同，返回1.0
        if clean_title1 == clean_title2:
            return 1.0

        # 使用序列匹配器计算基础相似度
        matcher = SequenceMatcher(None, clean_title1, clean_title2)
        base_score = matcher.ratio()

        # 特殊处理：如果都包含章节号，提取章节号进行比较
        chapter_num1 = self._extract_chapter_number(title1)
        chapter_num2 = self._extract_chapter_number(title2)

        if chapter_num1 is not None and chapter_num2 is not None:
            if chapter_num1 == chapter_num2:
                # 章节号相同，大幅提升相似度
                return max(base_score, 0.8)

        return base_score

    def _extract_chapter_number(self, title: str) -> Optional[int]:
        """从标题中提取章节号"""
        # 匹配阿拉伯数字章节号
        match = re.search(r'第\s*(\d+)\s*章', title)
        if match:
            return int(match.group(1))

        # 匹配中文数字章节号
        chinese_nums = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '零': 0
        }

        match = re.search(r'第([一二三四五六七八九十零]+)章', title)
        if match:
            chinese_num = match.group(1)
            # 简单的中文数字转换（只处理1-10）
            if chinese_num in chinese_nums:
                return chinese_nums[chinese_num]

        return None
    
    def _generate_corrected_content(self, original_content: str, 
                                  reordered_chapters: List[Dict[str, str]]) -> str:
        """生成校对后的内容"""
        lines = original_content.split('\n')
        
        # 找到书籍信息部分的结束位置
        book_info_end = 0
        for i, line in enumerate(lines):
            if "===========" in line or "简介:" in line:
                book_info_end = i + 1
                break
        
        # 保留书籍信息部分
        corrected_lines = lines[:book_info_end]
        
        # 添加校对后的章节
        for chapter in reordered_chapters:
            corrected_lines.append('')  # 空行分隔
            corrected_lines.append('')  # 空行分隔
            corrected_lines.append(chapter['title'])
            corrected_lines.append('')  # 空行分隔
            corrected_lines.append(chapter['content'])
        
        return '\n'.join(corrected_lines)
