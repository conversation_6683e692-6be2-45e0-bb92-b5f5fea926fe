import { Notification, Message } from 'element-ui'

// 通知工具类
class NotificationUtil {
  constructor() {
    this.defaultDuration = 4500
  }

  // 成功通知
  success(title, message = '', options = {}) {
    return Notification.success({
      title,
      message,
      duration: this.defaultDuration,
      ...options
    })
  }

  // 警告通知
  warning(title, message = '', options = {}) {
    return Notification.warning({
      title,
      message,
      duration: this.defaultDuration,
      ...options
    })
  }

  // 错误通知
  error(title, message = '', options = {}) {
    return Notification.error({
      title,
      message,
      duration: this.defaultDuration,
      ...options
    })
  }

  // 信息通知
  info(title, message = '', options = {}) {
    return Notification.info({
      title,
      message,
      duration: this.defaultDuration,
      ...options
    })
  }

  // 下载完成通知
  downloadComplete(novelName, format) {
    return this.success(
      '下载完成',
      `《${novelName}》已成功下载为 ${format.toUpperCase()} 格式`,
      {
        duration: 6000,
        onClick: () => {
          // 可以添加点击打开文件夹的逻辑
        }
      }
    )
  }

  // 下载失败通知
  downloadFailed(novelName, error) {
    return this.error(
      '下载失败',
      `《${novelName}》下载失败：${error}`,
      {
        duration: 8000
      }
    )
  }

  // 任务状态变更通知
  taskStatusChange(taskName, status) {
    const statusMap = {
      'processing': { type: 'info', title: '任务开始', message: `《${taskName}》开始下载` },
      'completed': { type: 'success', title: '任务完成', message: `《${taskName}》下载完成` },
      'failed': { type: 'error', title: '任务失败', message: `《${taskName}》下载失败` },
      'cancelled': { type: 'warning', title: '任务取消', message: `《${taskName}》已取消下载` }
    }

    const config = statusMap[status]
    if (config) {
      return this[config.type](config.title, config.message)
    }
  }

  // 激活状态通知
  activationSuccess(remainingDownloads) {
    return this.success(
      '激活成功',
      `软件已成功激活，剩余下载次数：${remainingDownloads}`,
      {
        duration: 6000
      }
    )
  }

  activationFailed(reason) {
    return this.error(
      '激活失败',
      reason || '激活码无效或已过期',
      {
        duration: 8000
      }
    )
  }

  // 网络状态通知
  networkOffline() {
    return this.warning(
      '网络连接异常',
      '请检查网络连接后重试',
      {
        duration: 0 // 不自动关闭
      }
    )
  }

  networkOnline() {
    return this.success(
      '网络已连接',
      '网络连接已恢复正常'
    )
  }

  // 更新通知
  updateAvailable(version) {
    return this.info(
      '发现新版本',
      `新版本 ${version} 已发布，建议及时更新`,
      {
        duration: 10000,
        onClick: () => {
          // 可以添加跳转到更新页面的逻辑
        }
      }
    )
  }

  // 清除所有通知
  closeAll() {
    Notification.closeAll()
  }
}

// 消息工具类
class MessageUtil {
  // 成功消息
  success(message, options = {}) {
    return Message.success({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 警告消息
  warning(message, options = {}) {
    return Message.warning({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 错误消息
  error(message, options = {}) {
    return Message.error({
      message,
      duration: 4000,
      showClose: true,
      ...options
    })
  }

  // 信息消息
  info(message, options = {}) {
    return Message.info({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 加载消息
  loading(message = '加载中...', options = {}) {
    return Message({
      message,
      type: 'info',
      duration: 0,
      iconClass: 'el-icon-loading',
      showClose: false,
      ...options
    })
  }

  // 关闭消息
  close(instance) {
    if (instance && instance.close) {
      instance.close()
    }
  }

  // 关闭所有消息
  closeAll() {
    Message.closeAll()
  }
}

// 创建实例
export const notification = new NotificationUtil()
export const message = new MessageUtil()

// 默认导出
export default {
  notification,
  message
}
