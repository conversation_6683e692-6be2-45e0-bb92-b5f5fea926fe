"""
番茄小说章节获取工具
改写自原JavaScript脚本：https://greasyfork.org/zh-CN/scripts/476688
"""

import json
import requests
import time
import re
from bs4 import BeautifulSoup

from ..base_system.context import GlobalContext

# 配置后端API地址
API_NODE = 'https://api.v2.sukimon.me:45598'


def sleep(seconds):
    """等待指定的秒数"""
    time.sleep(seconds)


def chunk_array(arr, size):
    """
    分割数组为指定大小的小数组

    Args:
        arr (list): 要分割的数组
        size (int): 每个分割数组的大小

    Returns:
        list: 分割后的数组列表
    """
    return [arr[i:i + size] for i in range(0, len(arr), size)]


def merge_objects(obj_arr):
    """
    合并多个字典为一个字典

    Args:
        obj_arr (list): 字典列表

    Returns:
        dict: 合并后的字典
    """
    result = {}
    for obj in obj_arr:
        result.update(obj)
    return result


def html_to_text_array(html):
    """
    获取HTML的纯文本内容

    Args:
        html (str): HTML字符串

    Returns:
        list: 文本内容列表
    """
    soup = BeautifulSoup(html, 'html.parser')
    text_array = []

    def extract_text(element):
        if element.string and element.string.strip():
            text_array.append(element.string.strip())
        for child in element.children:
            if child.name:
                extract_text(child)

    extract_text(soup)
    return text_array


def check_multi_content():
    """
    检查后端是否支持多章节获取

    Returns:
        bool: 是否支持多章节获取
    """
    try:
        response = requests.get(f"{API_NODE}/multi-content")
        if response.status_code == 404:
            print('后端API不支持多章节获取功能')
            return False
        else:
            try:
                data = response.json()
                print('后端API支持多章节获取功能')
                return True
            except:
                print('后端API响应不是有效的JSON')
                return False
    except Exception as e:
        print(f'后端API请求失败: {e}')
        return False


def get_book_info(book_id, retry=0):
    """
    获取书籍信息

    Args:
        book_id (str): 书籍ID
        retry (int): 重试次数

    Returns:
        dict: 书籍信息数据
    """
    if retry > 3:
        print(f"错误：超过最大重试次数 {retry}")
        return None

    api_url = f"{API_NODE}/catalog?book_id={book_id}"

    try:
        response = requests.get(api_url)
        data = response.json()

        if data.get('code') == 0:
            print(f'成功获取书籍 {book_id} 信息')
            return data
        else:
            print(f"获取书籍信息错误: {data.get('code')}, {data.get('msg', '')}")
            retry += 1
            print(f"第 {retry} 次重试...")
            return get_book_info(book_id, retry)
    except Exception as e:
        print(f"请求错误: {e}")
        retry += 1
        print(f"第 {retry} 次重试...")
        return get_book_info(book_id, retry)


def get_chapter_content(item_id, text_mode=False, image_mode=True, retry=0):
    """
    获取单章节内容

    Args:
        item_id (str): 章节ID
        text_mode (bool): 是否返回纯文本内容
        image_mode (bool): 是否返回图片内容
        retry (int): 重试次数

    Returns:
        dict: 章节内容数据
    """
    logger = GlobalContext.get_logger()
    
    if retry > 3:
        logger.error(f"获取章节 {item_id} 错误：超过最大重试次数 {retry}")
        return None

    api_url = f"{API_NODE}/content?item_id={item_id}&text_mode={str(text_mode).lower()}&image_mode={str(image_mode).lower()}"

    try:
        response = requests.get(api_url)
        data = response.json()

        if data.get('code') == 0:
            logger.info(f'成功获取章节 {item_id} 内容')
            return data
        else:
            logger.warning(f"获取章节 {item_id} 内容错误: {data.get('code')}, {data.get('msg', '')}")
            retry += 1
            logger.debug(f"第 {retry} 次重试获取章节 {item_id}")
            time.sleep(1)
            return get_chapter_content(item_id, text_mode, image_mode, retry)
    except Exception as e:
        logger.error(f"请求章节 {item_id} 错误: {e}")
        retry += 1
        logger.debug(f"第 {retry} 次重试获取章节 {item_id}")
        time.sleep(1)
        return get_chapter_content(item_id, text_mode, image_mode, retry)


def get_multiple_chapters(book_id, item_ids, text_mode=False, image_mode=True, retry=0):
    """
    获取多个章节内容

    Args:
        book_id (str): 书籍ID
        item_ids (list): 章节ID列表
        text_mode (bool): 是否返回纯文本内容
        image_mode (bool): 是否返回图片内容
        retry (int): 重试次数

    Returns:
        dict: 多章节内容数据
    """
    if retry > 5:
        print(f"错误：超过最大重试次数 {retry}")
        return None

    item_ids_str = ','.join(map(str, item_ids))
    api_url = f"{API_NODE}/multi-content?book_id={book_id}&text_mode={str(text_mode).lower()}&image_mode={str(image_mode).lower()}&item_ids={item_ids_str}"

    try:
        response = requests.get(api_url)
        data = response.json()

        if data.get('code') == 0:
            print(f'成功获取 {len(item_ids)} 个章节内容')
            return data.get('data', {})
        else:
            print(f"获取内容错误: {data.get('code')}, {data.get('msg', '')}")
            retry += 1
            print(f"第 {retry} 次重试...")
            return get_multiple_chapters(book_id, item_ids, text_mode, image_mode, retry)
    except Exception as e:
        print(f"请求错误: {e}")
        retry += 1
        print(f"第 {retry} 次重试...")
        return get_multiple_chapters(book_id, item_ids, text_mode, image_mode, retry)


def format_chapter_content(data):
    """
    格式化章节内容数据

    Args:
        data (dict): 章节内容数据

    Returns:
        dict: 格式化后的章节内容
    """
    logger = GlobalContext.get_logger()
    
    if not data or data.get('code') != 0:
        logger.error(f"格式化章节内容失败，无效的数据格式")
        return None

    result = {
        'title': None,
        'book_name': None,
        'volume_name': None,
        'content': None,
        'raw_data': data
    }

    # 提取章节信息
    novel_data = data.get('data', {}).get('novel_data', {})
    result['title'] = novel_data.get('title')
    result['book_name'] = novel_data.get('book_name')
    result['volume_name'] = novel_data.get('volume_name', '').replace('：默认', '')

    # 获取内容
    content = None
    if isinstance(data.get('data', {}).get('data'), dict):
        content = data.get('data', {}).get('data', {}).get('content')
    else:
        content = data.get('data', {}).get('content')

    # 处理HTML内容
    if content and ('<doctype' in content.lower() or '<article' in content.lower() or '<header>' in content.lower()):
        text_content = html_to_text_array(content)
        result['content'] = '\n　　'.join(text_content)
    else:
        result['content'] = content

    if result['content']:
        logger.debug(f"成功格式化章节内容: {result['title']}")
    else:
        logger.warning(f"章节内容为空: {result['title']}")
        
    return result


def format_catalog(data):
    """
    格式化书籍目录数据

    Args:
        data (dict): 书籍目录数据

    Returns:
        dict: 格式化后的书籍目录
    """
    if not data or data.get('code') != 0:
        return None

    book_info = {
        'book_id': data.get('data', {}).get('book_id'),
        'book_name': data.get('data', {}).get('book_info', {}).get('book_name'),
        'author_name': data.get('data', {}).get('book_info', {}).get('author_name'),
        'description': data.get('data', {}).get('book_info', {}).get('description'),
        'total_chapters': len(data.get('data', {}).get('item_list', [])),
        'chapters': []
    }

    for item in data.get('data', {}).get('item_list', []):
        chapter = {
            'item_id': item.get('item_id'),
            'title': item.get('title'),
            'volume_name': item.get('volume_name', ''),
            'auth_access': item.get('auth_access', False)
        }
        book_info['chapters'].append(chapter)

    return book_info 