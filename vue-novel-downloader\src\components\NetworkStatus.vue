<template>
  <div class="network-status" :class="{ 'offline': !isOnline }">
    <div class="status-indicator">
      <i :class="statusIcon"></i>
      <span class="status-text">{{ statusText }}</span>
    </div>
    
    <div v-if="!isOnline" class="offline-notice">
      <p>网络连接异常，部分功能可能无法使用</p>
      <el-button size="mini" @click="checkConnection">重新检测</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NetworkStatus',
  
  data() {
    return {
      isOnline: navigator.onLine,
      checking: false
    }
  },
  
  computed: {
    statusIcon() {
      if (this.checking) return 'el-icon-loading'
      return this.isOnline ? 'el-icon-success' : 'el-icon-warning'
    },
    
    statusText() {
      if (this.checking) return '检测中...'
      return this.isOnline ? '网络正常' : '网络异常'
    }
  },
  
  mounted() {
    // 监听网络状态变化
    window.addEventListener('online', this.handleOnline)
    window.addEventListener('offline', this.handleOffline)
    
    // 初始检测
    this.checkConnection()
  },
  
  beforeDestroy() {
    window.removeEventListener('online', this.handleOnline)
    window.removeEventListener('offline', this.handleOffline)
  },
  
  methods: {
    handleOnline() {
      this.isOnline = true
      this.$emit('status-change', true)
    },
    
    handleOffline() {
      this.isOnline = false
      this.$emit('status-change', false)
    },
    
    async checkConnection() {
      this.checking = true

      try {
        // 尝试访问番茄小说官方网站检测网络连接
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000)

        const response = await fetch('https://fanqienovel.com', {
          method: 'HEAD', // 只获取头部信息，减少流量
          signal: controller.signal,
          mode: 'no-cors' // 避免CORS问题
        })

        clearTimeout(timeoutId)
        this.isOnline = true // 如果请求没有抛出异常，说明网络正常
      } catch (error) {
        console.warn('网络检测失败:', error)
        // 如果是AbortError，说明超时，网络可能有问题
        this.isOnline = error.name !== 'AbortError'
      } finally {
        this.checking = false
        this.$emit('status-change', this.isOnline)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.network-status {
  padding: 8px 12px;
  border-radius: 6px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  margin-bottom: 15px;
  transition: all 0.3s;
  
  &.offline {
    background: #fef2f2;
    border-color: #fecaca;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 16px;
      color: #10b981;
      
      &.el-icon-warning {
        color: #f59e0b;
      }
      
      &.el-icon-loading {
        color: #6b7280;
      }
    }
    
    .status-text {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }
  }
  
  .offline-notice {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
    
    p {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: #6b7280;
    }
  }
}
</style>
