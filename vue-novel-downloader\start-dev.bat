@echo off
echo 🚀 启动某茄小说下载器开发环境
echo.

echo 📡 启动代理服务器 (端口 3001)...
start "代理服务器" cmd /k "node proxy-server.js"

echo ⏳ 等待代理服务器启动...
timeout /t 3 /nobreak >nul

echo 🌐 启动Vue开发服务器 (端口 8082)...
start "Vue开发服务器" cmd /k "npm run serve"

echo.
echo ✅ 开发环境启动完成！
echo.
echo 📖 应用地址: http://localhost:8082
echo 🔧 代理服务: http://localhost:3001
echo 🧪 测试页面: file:///%~dp0test-parser.html
echo.
echo 按任意键退出...
pause >nul
