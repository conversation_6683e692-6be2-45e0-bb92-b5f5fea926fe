"""
app.py - GUI应用启动模块
"""
import sys
import time
from PyQt5.QtWidgets import QApplication, QDialog, QMessageBox

from ..base_system.context import GlobalContext
from ..base_system.activation import ActivationManager
from ..base_system.updater import Updater
from ..network_parser.network import NetworkClient
from .main_window import MainWindow
from .login_window import LoginWindow
from .update_window import UpdateWindow


def start_gui(debug_mode=False):
    """启动GUI应用"""
    # 初始化全局上下文
    GlobalContext(debug_mode)
    logger = GlobalContext.get_logger()
    config = GlobalContext.get_config()

    # 初始化全局网络客户端并设置
    network_client = NetworkClient()
    GlobalContext.set_network_client(network_client)

    # 创建应用实例
    app = QApplication(sys.argv)

    # 激活检查
    activation_manager = ActivationManager()

    # 创建变量来存储主窗口引用
    main_window = None

    # 首次启动直接检查更新
    if config.auto_check_update and should_check_update(config):
        logger.info("启动时检查更新")
        updater = Updater()
        has_update, message, update_info = updater.check_update()

        # 更新最后检查时间
        config.last_update_check = int(time.time())
        config.save()

        if has_update:
            # 有更新，显示更新窗口
            logger.info(f"发现新版本: {update_info.get('version')}")

            # 检查是否为强制更新
            is_mandatory = update_info.get("isMandatory", False)

            if is_mandatory:
                # 强制更新，不给用户跳过的选择
                QMessageBox.warning(
                    None,
                    "强制更新",
                    f"发现新版本 v{update_info.get('version')}，该版本为强制更新，必须更新后才能继续使用。\n请加入QQ群下载最新版本。",
                    QMessageBox.Ok
                )

                # 显示更新窗口并禁用关闭按钮
                update_window = UpdateWindow(auto_check=False, allow_close=False)
                update_window.info_text.setHtml(get_update_info_html(update_info, is_mandatory=True))
                update_window.join_qq_button.setEnabled(True)
                update_window.status_label.setText(message)
                update_result = update_window.exec_()

                # 如果用户关闭窗口或取消更新，则退出应用程序
                if update_result != QDialog.Accepted:
                    logger.info("用户取消强制更新，程序退出")
                    sys.exit(0)
            else:
                # 非强制更新，询问用户是否更新
                reply = QMessageBox.question(
                    None,
                    "发现新版本",
                    f"发现新版本 v{update_info.get('version')}，是否现在更新？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # 显示更新窗口
                    update_window = UpdateWindow(auto_check=False)
                    update_window.info_text.setHtml(get_update_info_html(update_info))
                    update_window.join_qq_button.setEnabled(True)
                    update_window.status_label.setText(message)
                    update_window.exec_()

    if activation_manager.is_activated():
        logger.info("软件已激活，直接启动主窗口")
        main_window = show_main_window()
    else:
        # 显示登录窗口
        login_window = LoginWindow()

        # 连接登录成功信号到显示主窗口的槽函数
        def on_login_success():
            nonlocal main_window
            logger.info("登录成功，正在跳转到主界面")
            main_window = show_main_window()

        login_window.login_success.connect(on_login_success)

        # 显示登录窗口并等待结果
        result = login_window.exec_()

        # 如果登录窗口被拒绝（用户点击取消或关闭），则结束应用
        if not activation_manager.is_activated() and result != QDialog.Accepted:
            logger.info("用户未激活应用，程序退出")
            sys.exit(0)

    # 确保主窗口不会被垃圾回收
    app.main_window = main_window

    # 进入事件循环
    return app.exec_()


def show_main_window():
    """显示主窗口并返回其引用"""
    main_window = MainWindow()
    main_window.show()
    return main_window


def should_check_update(config):
    """判断是否应该检查更新"""
    current_time = int(time.time())
    last_check = config.last_update_check
    interval = config.update_check_interval * 3600  # 转换为秒

    # 如果从未检查过，或者已经超过了检查间隔时间，则应当检查
    return last_check == 0 or (current_time - last_check) > interval


def get_update_info_html(update_info, is_mandatory=False):
    """生成更新信息HTML"""
    version = update_info.get("version", "未知")
    release_date = update_info.get("releaseDate", "未知")
    changelog = update_info.get("changelog", "无")

    # 显示更新信息
    info_text = f"<h3>发现新版本: v{version}" + (" <span style='color:red;'>(强制更新)</span>" if is_mandatory else "") + "</h3>\n"
    info_text += f"<p>发布日期: {release_date}</p>\n"
    if is_mandatory:
        info_text += "<p style='color:red; font-weight:bold;'>此版本为强制更新，您必须更新后才能继续使用软件！</p>\n"
    info_text += f"<h4>更新内容:</h4>\n"
    info_text += f"<p>{changelog}</p>"

    # 添加QQ群下载提示
    info_text += "<p style='margin-top:15px; font-weight:bold;'>请点击「加入QQ群下载」按钮，在QQ群文件中下载最新版本。</p>"

    return info_text