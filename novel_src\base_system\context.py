# -------------------------------
# config.py - 配置管理模块
# 职责：集中管理所有配置项和路径
# -------------------------------
import os
import re
from logging import Logger
from pathlib import Path

from .storge_system import BaseConfig, Field, ConfigError
# 避免循环导入，将 LogSystem 的导入移到 GlobalContext 类的实现内部
# from .log_system import LogSystem
from ..constants import VERSION, CLIENT_VERSION


class Config(BaseConfig):
    """Config 配置文件"""

    # 网络配置
    max_workers: int = Field(default=3, description="最大并发线程数")
    request_timeout: int = Field(default=15, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    max_wait_time: int = Field(default=1200, description="最大冷却时间, 单位ms")
    min_wait_time: int = Field(default=1000, description="最小冷却时间, 单位ms")
    min_connect_timeout: float = Field(default=3.05, description="最小连接超时时间")
    force_exit_timeout: int = Field(default=5, description="强制退出等待时间")
    graceful_exit: bool = Field(default=True, description="是否启用优雅退出")

    # 保存配置
    novel_format: str = Field(
        default="txt", description="保存小说格式, 可选: [txt, epub]"
    )
    auto_clear_dump: bool = Field(default=True, description="是否自动清理缓存文件")

    # 路径配置
    status_filename: str = Field(
        default="chapter_status.json", description="下载状态文件名"
    )
    cookie_filename: str = Field(
        default="session_cookie.json", description="Cookie存储文件名"
    )
    save_path: str = Field(default="", description="保存路径")
    qimao_save_dir: str = Field(default="", description="七猫小说保存路径")

    # API配置
    use_official_api: bool = Field(default=True, description="使用官方API")
    iid: str = Field(default="", description="自动生成")
    api_endpoints: list = Field(
        default=[],
        description="API列表",
    )
    
    # 激活配置
    activation_check: bool = Field(default=True, description="是否启用激活检查")
    activation_token: str = Field(default="", description="激活码")
    machine_code: str = Field(default="", description="机器码")
    
    # 更新配置
    auto_check_update: bool = Field(default=True, description="是否自动检查更新")
    update_check_interval: int = Field(default=24, description="更新检查间隔（小时）")
    last_update_check: int = Field(default=0, description="上次检查更新时间戳")
    
    # 客户端版本
    client_version: str = Field(default=CLIENT_VERSION, description="客户端版本号")
    
    # 任务查询配置
    task_check_interval: int = Field(default=8, description="任务查询间隔（秒）")
    task_max_check_times: int = Field(default=120, description="最大任务查询次数")

    @property
    def default_save_dir(self) -> Path:
        """获取默认保存目录路径对象"""
        if self.save_path:
            return Path(self.save_path)
        else:
            return Path(os.getcwd())

    def status_file_path(self, save_dir: str, book_id: str) -> Path:
        """生成书籍专属状态文件路径"""
        # 清理非法字符确保文件名安全
        safe_book_id = re.sub(r"[^a-zA-Z0-9_]", "_", book_id)
        filename = f"chapter_status_{safe_book_id}.json"
        return Path(save_dir) / filename


class GlobalContext(object):
    def __init__(self, debug: bool, config: BaseConfig = Config):
        """创建公用Context"""
        # 延迟导入，避免循环依赖
        from .log_system import LogSystem
        
        global _log_system, _config
        _log_system = LogSystem(debug=debug)
        try:
            _config = config.load()
        except ConfigError as e:
            _log_system.logger.error(f"配置操作失败: {str(e)}")
        
        # 添加全局网络客户端
        global _network_client
        _network_client = None

    @staticmethod
    def get_logger() -> Logger:
        """获取logger"""
        return _log_system.logger

    @staticmethod
    def get_log_system():
        """获取log_system"""
        # 明确指定返回类型，避免需要导入 LogSystem
        return _log_system

    @staticmethod
    def get_config() -> Config:
        """获取Config"""
        return _config
        
    @staticmethod
    def get_network_client():
        """获取网络客户端，如果需要延迟初始化"""
        global _network_client
        return _network_client
    
    @staticmethod
    def set_network_client(client):
        """设置全局网络客户端"""
        global _network_client
        _network_client = client
        
    @staticmethod
    def get_token() -> str:
        """获取激活码"""
        return _config.activation_token
