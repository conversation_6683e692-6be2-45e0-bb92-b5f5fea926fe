"""
update_window.py - 更新窗口模块
"""
import os
import sys
import webbrowser
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QProgressBar, QMessageBox, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QPixmap, QIcon

from ..base_system.context import GlobalContext
from ..base_system.updater import Updater
from ..constants import VERSION, QQ_GROUP_URL


class UpdateWindow(QDialog):
    """更新窗口"""

    # 定义信号
    update_finished = pyqtSignal(bool, str)  # 更新完成信号

    def __init__(self, parent=None, auto_check=True, allow_close=True):
        super().__init__(parent)

        # 初始化基础组件
        self.logger = GlobalContext.get_logger()
        self.updater = Updater()
        self.update_info = None
        self.update_file_path = None
        self.allow_close = allow_close  # 是否允许关闭窗口

        # 设置窗口属性
        self.setWindowTitle("软件更新")
        self.resize(500, 400)
        self.setMinimumSize(400, 300)

        # 设置关闭窗口行为
        if not allow_close:
            # 如果不允许关闭，则禁用关闭按钮
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowCloseButtonHint)

        # 初始化UI
        self.init_ui()

        # 如果设置了自动检查，则立即检查更新
        if auto_check:
            self.check_update()

    def init_ui(self):
        """初始化UI组件"""
        main_layout = QVBoxLayout(self)

        # 标题区域
        title_layout = QHBoxLayout()

        # 标题图标
        icon_label = QLabel()
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "img", "update_icon.png")
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path)
            icon_label.setPixmap(pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        title_layout.addWidget(icon_label)

        # 标题和当前版本
        title_info_layout = QVBoxLayout()
        title_label = QLabel("Useful小说下载器更新")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        version_label = QLabel(f"当前版本: v{VERSION}")

        title_info_layout.addWidget(title_label)
        title_info_layout.addWidget(version_label)
        title_layout.addLayout(title_info_layout)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 更新信息区域
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setPlaceholderText("正在获取更新信息...")
        main_layout.addWidget(self.info_text)

        # 状态标签
        self.status_label = QLabel("正在检查更新...")
        main_layout.addWidget(self.status_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 检查更新按钮
        self.check_button = QPushButton("检查更新")
        self.check_button.clicked.connect(self.check_update)
        button_layout.addWidget(self.check_button)

        # 加入QQ群按钮（替代原来的下载更新按钮）
        self.join_qq_button = QPushButton("跳转教程")
        self.join_qq_button.clicked.connect(self.open_qq_group)
        self.join_qq_button.setEnabled(False)
        button_layout.addWidget(self.join_qq_button)

        # 关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.handle_close)
        self.close_button.setEnabled(self.allow_close)  # 仅在允许关闭时启用
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def closeEvent(self, event):
        """重写关闭事件，处理强制更新时的关闭行为"""
        if not self.allow_close:
            # 如果不允许关闭，显示提示并退出程序
            QMessageBox.warning(
                self,
                "强制更新",
                "当前版本为强制更新版本，您必须更新后才能继续使用软件！\n请加入QQ群下载最新版本。",
                QMessageBox.Ok
            )
            # 直接退出程序
            self.logger.info("强制更新模式：用户关闭窗口，程序将退出")
            sys.exit(0)
        else:
            event.accept()

    def handle_close(self):
        """处理关闭按钮点击事件"""
        if self.allow_close:
            self.close()
        else:
            # 显示提示后直接退出程序
            result = QMessageBox.warning(
                self,
                "强制更新",
                "当前版本为强制更新版本，您必须更新后才能继续使用软件！\n请加入QQ群下载最新版本。",
                QMessageBox.Ok
            )
            # 用户点击 OK 按钮后直接退出程序
            self.logger.info("强制更新模式：用户点击OK按钮，程序将退出")
            sys.exit(0)

    def check_update(self):
        """检查更新"""
        # 禁用检查按钮，避免重复点击
        self.check_button.setEnabled(False)
        self.status_label.setText("正在检查更新...")

        # 执行检查
        has_update, message, update_info = self.updater.check_update()

        # 处理结果
        self.status_label.setText(message)

        if has_update:
            # 保存更新信息
            self.update_info = update_info

            # 检查是否为强制更新
            is_mandatory = update_info.get("isMandatory", False)

            # 构建更新信息文本
            version = update_info.get("version", "未知")
            release_date = update_info.get("updateDate", "未知")
            changelog = update_info.get("updateDescription", "无")

            # 显示更新信息
            info_text = f"<h3>发现新版本: v{version}" + (" <span style='color:red;'>(强制更新)</span>" if is_mandatory else "") + "</h3>\n"
            info_text += f"<p>发布日期: {release_date}</p>\n"
            if is_mandatory:
                info_text += "<p style='color:red; font-weight:bold;'>此版本为强制更新，您必须更新后才能继续使用软件！</p>\n"
            info_text += f"<h4>更新内容:</h4>\n"
            info_text += f"<p>{changelog}</p>"

            # 添加QQ群下载提示
            info_text += "<p style='margin-top:15px; font-weight:bold;'>请点击「跳转教程」按钮，在QQ群文件中下载最新版本。</p>"

            self.info_text.setHtml(info_text)

            # 启用加入QQ群按钮
            self.join_qq_button.setEnabled(True)

            # 如果是强制更新，禁用关闭按钮
            self.close_button.setEnabled(not is_mandatory)
            self.allow_close = not is_mandatory
        else:
            # 没有更新
            self.info_text.setHtml(f"<p>{message}</p>")

            # 禁用加入QQ群按钮
            self.join_qq_button.setEnabled(False)

            # 启用关闭按钮
            self.close_button.setEnabled(True)
            self.allow_close = True

        # 恢复检查按钮
        self.check_button.setEnabled(True)

    def open_qq_group(self):
        """打开QQ群链接"""
        try:
            webbrowser.open(QQ_GROUP_URL)
            self.status_label.setText("已打开QQ群链接，请在QQ群文件中下载最新版本")

            if not self.allow_close:
                # 如果是强制更新，提示用户并退出程序
                QMessageBox.information(
                    self,
                    "更新提示",
                    "已为您打开链接，请在QQ群文件中下载安装最新版本。\n\n软件将立即退出，安装新版本后再重新启动。",
                    QMessageBox.Ok
                )
                self.logger.info("强制更新模式：已打开QQ群链接，程序将退出")
                sys.exit(0)
            else:
                # 非强制更新，只显示提示
                QMessageBox.information(
                    self,
                    "更新提示",
                    "已为您打开链接，请在QQ群文件中下载安装最新版本。",
                    QMessageBox.Ok
                )
        except Exception as e:
            error_msg = f"打开链接失败: {str(e)}"
            self.logger.error(error_msg)
            QMessageBox.critical(
                self,
                "打开链接失败",
                f"无法打开QQ群链接，请手动复制以下地址到浏览器打开：\n\n{QQ_GROUP_URL}",
                QMessageBox.Ok
            )