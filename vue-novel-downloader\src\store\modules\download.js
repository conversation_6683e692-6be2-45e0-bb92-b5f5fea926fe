// 下载状态管理
const state = {
  // 当前下载信息
  currentDownload: {
    platform: '', // fanqie, qimao
    novelId: '',
    novelName: '',
    novelAuthor: '',
    novelDesc: '',
    novelUrl: '',
    coverUrl: '',
    type: 'web',
    totalChapters: 0,
    selectedChapters: [], // 全部章节ID
    chapterTitles: [], // 全部章节标题
    format: 'txt'
  },

  // 章节列表
  chapterList: [],

  // 下载状态
  downloadStatus: {
    isDownloading: false,
    progress: 0,
    currentChapter: '',
    speed: 0,
    eta: 0,
    startTime: null,
    endTime: null,
    taskId: '', // 任务ID
    status: '', // 任务状态
    fileUrl: '', // 文件下载URL
    errorMessage: '', // 错误信息
    downloadCompleted: false // 下载完成标志
  },

  // 错误信息
  errors: []
}

const mutations = {
  SET_CURRENT_DOWNLOAD(state, downloadInfo) {
    state.currentDownload = { ...state.currentDownload, ...downloadInfo }
  },

  SET_CHAPTER_LIST(state, chapters) {
    state.chapterList = chapters
  },

  SET_SELECTED_CHAPTERS(state, selectedChapters) {
    state.currentDownload.selectedChapters = selectedChapters
  },

  SET_CHAPTER_TITLES(state, chapterTitles) {
    state.currentDownload.chapterTitles = chapterTitles
  },

  SET_DOWNLOAD_STATUS(state, status) {
    state.downloadStatus = { ...state.downloadStatus, ...status }
  },

  START_DOWNLOAD(state) {
    state.downloadStatus.isDownloading = true
    state.downloadStatus.startTime = new Date()
    state.downloadStatus.progress = 0
    state.errors = []
  },

  STOP_DOWNLOAD(state) {
    state.downloadStatus.isDownloading = false
    state.downloadStatus.endTime = new Date()
  },

  UPDATE_PROGRESS(state, { progress, currentChapter, speed, eta }) {
    state.downloadStatus.progress = progress
    if (currentChapter) state.downloadStatus.currentChapter = currentChapter
    if (speed !== undefined) state.downloadStatus.speed = speed
    if (eta !== undefined) state.downloadStatus.eta = eta
  },

  ADD_ERROR(state, error) {
    state.errors.push({
      id: Date.now(),
      message: error.message || error,
      time: new Date(),
      type: error.type || 'error'
    })
  },

  CLEAR_ERRORS(state) {
    state.errors = []
  },

  RESET_DOWNLOAD(state) {
    state.currentDownload = {
      platform: '',
      novelId: '',
      novelName: '',
      novelAuthor: '',
      novelDesc: '',
      novelUrl: '',
      coverUrl: '',
      type: 'web',
      totalChapters: 0,
      selectedChapters: [],
      chapterTitles: [],
      format: 'txt'
    }
    state.chapterList = []
    state.downloadStatus = {
      isDownloading: false,
      progress: 0,
      currentChapter: '',
      speed: 0,
      eta: 0,
      startTime: null,
      endTime: null,
      taskId: '',
      status: '',
      fileUrl: '',
      errorMessage: '',
      downloadCompleted: false
    }
    state.errors = []
  }
}

const actions = {
  // 设置当前下载
  setCurrentDownload({ commit }, downloadInfo) {
    commit('SET_CURRENT_DOWNLOAD', downloadInfo)
  },

  // 获取章节列表
  async fetchChapterList({ commit, state }, { platform, novelId }) {
    try {
      // 这里调用API获取章节列表
      // const response = await api.getChapterList(platform, novelId)
      // const chapters = response.data

      // 模拟数据
      const chapters = Array.from({ length: 100 }, (_, index) => ({
        id: `chapter_${index + 1}`,
        title: `第${index + 1}章 章节标题${index + 1}`,
        index: index + 1,
        wordCount: Math.floor(Math.random() * 3000) + 1000,
        isVip: index > 50,
        isSelected: true
      }))

      commit('SET_CHAPTER_LIST', chapters)
      commit('SET_CURRENT_DOWNLOAD', {
        totalChapters: chapters.length,
        selectedChapters: chapters.filter(ch => ch.isSelected).map(ch => ch.id)
      })

      return chapters
    } catch (error) {
      commit('ADD_ERROR', { message: '获取章节列表失败', type: 'error' })
      throw error
    }
  },

  // 选择章节
  selectChapters({ commit, state }, selectedIds) {
    const updatedChapters = state.chapterList.map(chapter => ({
      ...chapter,
      isSelected: selectedIds.includes(chapter.id)
    }))

    commit('SET_CHAPTER_LIST', updatedChapters)
    commit('SET_SELECTED_CHAPTERS', selectedIds)
  },

  // 开始下载 - 基于Python版本的API逻辑
  async startDownload({ commit, state, rootGetters, dispatch }) {
    try {
      commit('START_DOWNLOAD')

      // 构建请求数据，对应Python版本的request_novel_download方法（简化版本）
      const downloadData = {
        tokenName: rootGetters['user/activationToken'],
        novelId: state.currentDownload.novelId,
        novelName: state.currentDownload.novelName,
        novelAuthor: state.currentDownload.novelAuthor,
        novelDesc: state.currentDownload.novelDesc,
        novelChapterIds: [],
        novelChapterTitles: [],
        type: "web",
        clientVersion: "2.2.0", // 对应Python版本的client_version
        formatChoice: state.currentDownload.format // 对应Python版本的format_choice
        // 不再传递 novelChapterIds 和 novelChapterTitles
      }

      // 使用统一的下载API
      const api = require('@/api').default
      const response = await api.download.requestNovelDownload(downloadData)

      if (response && (response.code === 200 || response.code === "200")) {
        console.log('下载请求响应:', response)

        // 获取任务ID，开始轮询下载进度
        const taskId = response.data?.taskId || response.taskId
        if (taskId) {
          // 保存任务ID到state
          commit('SET_DOWNLOAD_STATUS', { taskId })
          // 开始轮询下载进度
          dispatch('pollDownloadProgress', taskId)
          return { success: true, taskId }
        } else {
          throw new Error('未获取到任务ID')
        }
      } else {
        throw new Error(response?.msg || response?.message || '启动下载失败')
      }
    } catch (error) {
      commit('ADD_ERROR', { message: '下载失败: ' + error.message, type: 'error' })
      commit('STOP_DOWNLOAD')
      throw error
    }
  },

  // 轮询下载进度 - 基于Python版本的check_download_task方法
  pollDownloadProgress({ commit, state }, taskId) {
    const pollInterval = setInterval(async () => {
      try {
        // 使用统一的下载API查询任务状态
        const api = require('@/api').default
        const result = await api.download.checkDownloadTask(taskId)

        if (result && result.code === 200) {
          const taskInfo = result.data || {}

          // 映射状态，对应Python版本的状态映射
          const statusMap = {
            "PENDING": "pending",
            "PROCESSING": "processing",
            "COMPLETED": "completed",
            "FAILED": "failed"
          }

          const rawStatus = taskInfo.status || ""
          const status = statusMap[rawStatus] || "unknown"

          // 处理进度值
          let progress = 0
          try {
            progress = parseInt(taskInfo.progress || 0)
          } catch (e) {
            progress = 0
          }

          const fileUrl = taskInfo.fileUrl || ""
          const errorMsg = taskInfo.errorMsg || ""

          commit('UPDATE_PROGRESS', {
            progress,
            currentChapter: `进度: ${progress}%`,
            speed: 0, // 服务器端暂不提供速度信息
            eta: 0,   // 服务器端暂不提供预计时间
            status,
            fileUrl,
            errorMessage: errorMsg
          })

          // 如果下载完成，停止轮询并处理文件下载
          if (status === 'completed' && progress >= 100) {
            clearInterval(pollInterval)
            commit('STOP_DOWNLOAD')

            // 如果有文件URL，触发文件下载
            if (fileUrl) {
              commit('SET_DOWNLOAD_STATUS', {
                fileUrl,
                downloadCompleted: true
              })
              // 可以在这里添加自动下载文件的逻辑
            }
          } else if (status === 'failed') {
            clearInterval(pollInterval)
            commit('STOP_DOWNLOAD')
            commit('ADD_ERROR', {
              message: `下载失败: ${errorMsg || '未知错误'}`,
              type: 'error'
            })
          }
        } else {
          console.error('查询任务状态失败:', result.msg || result.message)
        }
      } catch (error) {
        console.error('轮询下载进度失败:', error)
        // 不立即停止轮询，允许网络错误后重试
      }
    }, 2000) // 每2秒轮询一次
  },

  // 停止下载
  stopDownload({ commit }) {
    commit('STOP_DOWNLOAD')
  },

  // 重置下载
  resetDownload({ commit }) {
    commit('RESET_DOWNLOAD')
  },

  // 清除错误
  clearErrors({ commit }) {
    commit('CLEAR_ERRORS')
  },

  // 下载文件 - 基于Python版本的download_novel_file方法
  async downloadFile({ commit, state }, { fileUrl, fileName, formatType = 'txt' }) {
    try {
      if (!fileUrl) {
        throw new Error('文件URL不能为空')
      }

      const finalFileName = fileName || `${state.currentDownload.novelName}.${formatType}`

      try {
        // 方法1：使用fetch下载并创建blob
        const response = await fetch(fileUrl)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = finalFileName
        link.style.display = 'none'

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

      } catch (fetchError) {
        console.warn('Fetch下载失败，尝试直接链接下载:', fetchError)

        // 方法2：直接使用链接下载（备用方案）
        const link = document.createElement('a')
        link.href = fileUrl
        link.download = finalFileName
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        link.style.display = 'none'

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      commit('ADD_ERROR', {
        message: `开始下载文件: ${finalFileName}`,
        type: 'success'
      })

      return true
    } catch (error) {
      commit('ADD_ERROR', {
        message: `文件下载失败: ${error.message}`,
        type: 'error'
      })
      return false
    }
  },

  // 检查用户额度 - 基于Python版本的get_user_quota方法
  async checkUserQuota({ commit, rootGetters }) {
    try {
      const token = rootGetters['user/activationToken']
      if (!token) {
        throw new Error('未找到激活码')
      }

      const baseUrl = 'http://toolbox.zjzaki.cn/prod-api'
      const response = await fetch(`${baseUrl}/fq_token/get_quota?tokenName=${token}&clientVersion=2.2.0`, {
        method: 'GET'
      })

      if (response.status === 200) {
        const result = await response.json()

        if (result.code === 200 || result.code === "200") {
          const quotaData = result.data || {}
          return {
            success: true,
            quota: quotaData
          }
        } else {
          throw new Error(result.msg || result.message || '获取额度信息失败')
        }
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      commit('ADD_ERROR', {
        message: `获取用户额度失败: ${error.message}`,
        type: 'error'
      })
      return {
        success: false,
        error: error.message
      }
    }
  }
}

const getters = {
  currentDownload: state => state.currentDownload,
  chapterList: state => state.chapterList,
  downloadStatus: state => state.downloadStatus,
  errors: state => state.errors,

  // 计算属性
  selectedChapterCount: state => state.currentDownload.selectedChapters.length,
  totalWordCount: state => {
    return state.chapterList
      .filter(ch => ch.isSelected)
      .reduce((total, ch) => total + (ch.wordCount || 0), 0)
  },
  downloadDuration: state => {
    if (!state.downloadStatus.startTime) return 0
    const endTime = state.downloadStatus.endTime || new Date()
    return Math.floor((endTime - state.downloadStatus.startTime) / 1000)
  },
  isDownloading: state => state.downloadStatus.isDownloading,
  downloadProgress: state => state.downloadStatus.progress
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
