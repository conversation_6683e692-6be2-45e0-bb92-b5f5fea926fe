<template>
  <div class="activation-page">
    <div class="activation-container">
      <!-- 背景装饰 -->
      <div class="background-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>

      <!-- 激活卡片 -->
      <el-card class="activation-card" shadow="always">
        <div class="card-header">
          <div class="logo">
            <i class="el-icon-reading"></i>
            <h1>某茄小说下载器</h1>
          </div>
          <p class="subtitle">请激活软件以使用完整功能</p>
        </div>

        <div class="activation-content">
          <!-- 激活状态显示 -->
          <div v-if="isActivated" class="activation-success">
            <div class="success-icon">
              <i class="el-icon-success"></i>
            </div>
            <h2>激活成功！</h2>
            <p>欢迎使用某茄小说下载器</p>
            <div class="user-info">
              <p><strong>激活码：</strong>{{ maskedToken }}</p>
              <p><strong>剩余次数：</strong>{{ remainingDownloads }}</p>
              <p><strong>激活时间：</strong>{{ activationTime }}</p>
            </div>
            <el-button type="primary" size="large" @click="goToMain">
              开始使用
            </el-button>
          </div>

          <!-- 激活表单 -->
          <div v-else class="activation-form">
            <el-form :model="form" :rules="rules" ref="activationForm" label-position="top">


              <el-form-item label="激活码" prop="activationToken">
                <el-input
                  v-model="form.activationToken"
                  placeholder="请输入激活码"
                  show-password
                  clearable
                  @keyup.enter.native="submitActivation"
                >
                  <i slot="prefix" class="el-icon-key"></i>
                </el-input>
                <div class="form-tip">
                  请输入从官方渠道获取的激活码
                </div>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  size="large"
                  :loading="activating"
                  @click="submitActivation"
                  class="activation-button"
                >
                  {{ activating ? '激活中...' : '激活软件' }}
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 激活说明 -->
            <div class="activation-help">
              <h3>激活说明</h3>
              <ul>
                <li>激活码可在官方网站或授权渠道获取</li>
                <li>输入正确的激活码即可激活软件</li>
                <li>激活后可享受完整的下载功能</li>
                <li>如有问题请联系客服支持</li>
              </ul>

              <!-- 测试激活码提示 -->
              <div class="test-codes">
                <h4>测试激活码</h4>
                <div class="test-code-list">
                  <el-tag type="success" @click="useTestCode('TEST123')">TEST123 (未使用)</el-tag>
                  <el-tag type="info" @click="useTestCode('VALID888')">VALID888 (已绑定)</el-tag>
                  <el-tag type="warning" @click="useTestCode('DEMO')">DEMO (演示)</el-tag>
                  <el-tag type="danger" @click="useTestCode('USED456')">USED456 (不存在)</el-tag>
                </div>
                <p class="test-tip">点击上方标签可快速填入测试激活码</p>
              </div>


            </div>
          </div>
        </div>
      </el-card>

      <!-- 功能预览 -->
      <div class="feature-preview">
        <h2>软件功能</h2>
        <div class="feature-grid">
          <div class="feature-item">
            <i class="el-icon-download"></i>
            <h3>批量下载</h3>
            <p>支持批量下载小说章节，提高下载效率</p>
          </div>
          <div class="feature-item">
            <i class="el-icon-document"></i>
            <h3>多格式支持</h3>
            <p>支持TXT、EPUB等多种格式导出</p>
          </div>
          <div class="feature-item">
            <i class="el-icon-s-order"></i>
            <h3>任务管理</h3>
            <p>智能任务队列，支持断点续传</p>
          </div>
          <div class="feature-item">
            <i class="el-icon-setting"></i>
            <h3>个性化设置</h3>
            <p>丰富的配置选项，满足不同需求</p>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Activation',

  data() {
    return {
      form: {
        activationToken: ''
      },

      rules: {
        activationToken: [
          { required: true, message: '请输入激活码', trigger: 'blur' },
          { min: 3, message: '激活码长度不能少于3位', trigger: 'blur' }
        ]
      },

      activating: false
    }
  },

  computed: {
    ...mapGetters({
      isActivated: 'user/isActivated',
      activationToken: 'user/activationToken',
      userInfo: 'user/userInfo',
      remainingDownloads: 'user/remainingDownloads'
    }),

    maskedToken() {
      if (!this.activationToken) return ''
      const token = this.activationToken
      return token.length > 8 ? `${token.slice(0, 4)}****${token.slice(-4)}` : token
    },

    activationTime() {
      return this.userInfo?.activationTime ?
        new Date(this.userInfo.activationTime).toLocaleString() :
        '未知'
    }
  },

  async created() {
    // 检查激活状态，如果已激活则自动跳转
    const isActivated = await this.checkActivation()
    if (isActivated) {
      this.$message.success('检测到已激活，正在跳转...')
      setTimeout(() => {
        this.$router.replace('/fanqie').catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('路由跳转失败:', err)
          }
        })
      }, 1000)
    }
  },

  methods: {
    ...mapActions({
      activateUser: 'user/activateUser',
      checkActivation: 'user/checkActivation'
    }),



    // 提交激活
    async submitActivation() {
      try {
        await this.$refs.activationForm.validate()

        this.activating = true

        const result = await this.activateUser({
          token: this.form.activationToken
        })

        if (result.success === true) {
          this.$message.success(result.message)

          // 激活成功后自动跳转到主页
          setTimeout(() => {
            this.$router.replace('/fanqie').catch(err => {
              // 忽略重复导航错误
              if (err.name !== 'NavigationDuplicated') {
                console.error('路由跳转失败:', err)
              }
            })
          }, 1500)
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        if (error.message) {
          // 表单验证错误
          return
        }
      } finally {
        this.activating = false
      }
    },



    // 使用测试激活码
    useTestCode(code) {
      this.form.activationToken = code
      this.$message.info(`已填入测试激活码: ${code}`)
    },

    // 跳转到主页面
    goToMain() {
      this.$router.push('/fanqie').catch(err => {
        // 忽略重复导航错误
        if (err.name !== 'NavigationDuplicated') {
          console.error('路由跳转失败:', err)
        }
      })
    }






  }
}
</script>

<style lang="scss" scoped>
.activation-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }

  .activation-container {
    width: 100%;
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;

    .activation-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

      .card-header {
        text-align: center;
        padding: 30px 30px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

        .logo {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 15px;

          i {
            font-size: 48px;
            color: #409EFF;
            margin-bottom: 10px;
          }

          h1 {
            margin: 0;
            font-size: 24px;
            color: #303133;
            font-weight: 600;
          }
        }

        .subtitle {
          margin: 0;
          color: #606266;
          font-size: 16px;
        }
      }

      .activation-content {
        padding: 30px;
      }
    }

    .activation-success {
      text-align: center;

      .success-icon {
        font-size: 64px;
        color: #67C23A;
        margin-bottom: 20px;
      }

      h2 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 24px;
      }

      p {
        margin: 0 0 20px 0;
        color: #606266;
        font-size: 16px;
      }

      .user-info {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin: 20px 0;
        text-align: left;

        p {
          margin: 8px 0;
          color: #303133;
          font-size: 14px;
        }
      }
    }

    .activation-form {
      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        line-height: 1.4;

        .el-button {
          margin-left: 10px;
          padding: 0;
          font-size: 12px;
        }
      }

      .activation-button {
        width: 100%;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 25px;
      }

      .activation-help {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #EBEEF5;

        h3 {
          margin: 0 0 15px 0;
          color: #303133;
          font-size: 16px;
        }

        ul {
          margin: 0 0 20px 0;
          padding-left: 20px;

          li {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }

        .test-codes {
          margin: 20px 0;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          h4 {
            margin: 0 0 10px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
          }

          .test-code-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;

            .el-tag {
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }
          }

          .test-tip {
            margin: 0;
            font-size: 12px;
            color: #909399;
            text-align: center;
          }
        }

        .help-actions {
          display: flex;
          justify-content: space-around;
          flex-wrap: wrap;
          gap: 10px;
        }
      }
    }

    .feature-preview {
      color: white;

      h2 {
        margin: 0 0 30px 0;
        font-size: 28px;
        text-align: center;
        font-weight: 600;
      }

      .feature-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .feature-item {
          background: rgba(255, 255, 255, 0.1);
          padding: 25px;
          border-radius: 15px;
          text-align: center;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);

          i {
            font-size: 36px;
            margin-bottom: 15px;
            display: block;
          }

          h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: 600;
          }

          p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
          }
        }
      }
    }
  }
}







// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activation-page {
    .activation-container {
      grid-template-columns: 1fr;
      gap: 20px;

      .feature-preview {
        .feature-grid {
          grid-template-columns: 1fr;
        }
      }
    }

    .activation-card {
      .card-header {
        padding: 20px;

        .logo {
          h1 {
            font-size: 20px;
          }
        }
      }

      .activation-content {
        padding: 20px;
      }
    }
  }


}
</style>
