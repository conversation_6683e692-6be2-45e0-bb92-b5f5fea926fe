# 获取真实数据指南

## 问题说明

由于浏览器的CORS（跨域资源共享）安全策略，前端应用无法直接访问番茄小说的官方API。这导致无法获取真实的书籍和章节数据。

## 解决方案

### 方案1：使用简单代理服务器（推荐）

#### 1.1 快速启动

```bash
# 1. 启动代理服务器
node simple-proxy.js

# 2. 在另一个终端启动Vue应用
npm run serve
```

#### 1.2 自动启动脚本

双击运行 `start-with-proxy.bat` 文件，会自动：
1. 检查Node.js环境
2. 安装必要依赖
3. 启动代理服务器
4. 启动Vue开发服务器

#### 1.3 验证代理服务器

访问以下地址验证代理服务器是否正常：
- 健康检查: http://localhost:3001/health
- 测试端点: http://localhost:3001/test

#### 1.4 测试真实API

在浏览器中访问：
```
http://localhost:3001/api/reader/directory/detail?bookId=7416289834324479001
```

如果返回JSON数据，说明代理服务器工作正常。

### 方案2：使用浏览器扩展

#### 2.1 Chrome扩展

1. 安装 "CORS Unblock" 或 "Allow CORS" 扩展
2. 启用扩展
3. 刷新Vue应用页面
4. 现在可以直接访问官方API

#### 2.2 Firefox扩展

1. 安装 "CORS Everywhere" 扩展
2. 启用扩展
3. 重新加载页面

### 方案3：启动Chrome时禁用安全策略

#### Windows
```bash
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security --disable-features=VizDisplayCompositor
```

#### macOS
```bash
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security
```

#### Linux
```bash
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_dev_test"
```

## 代码修改

### 当前配置

Vue应用已经配置为自动检测环境：

```javascript
// src/api/fanqie.js
this.apiConfig = {
  // 开发环境使用代理服务器，生产环境直接访问
  useProxy: process.env.NODE_ENV === 'development',
  proxyUrl: 'http://localhost:3001/api',
  officialUrl: 'https://fanqienovel.com'
}
```

### API调用示例

```javascript
// 获取章节列表
const apiUrl = `${this.officialAPI.baseUrl}${this.officialAPI.chapterListUrl}?bookId=${bookId}`
// 开发环境: http://localhost:3001/api/reader/directory/detail?bookId=123
// 生产环境: https://fanqienovel.com/reader/directory/detail?bookId=123
```

## 验证真实数据

### 1. 启动代理服务器

```bash
node simple-proxy.js
```

看到以下输出表示启动成功：
```
🚀 简单代理服务器启动成功！

📍 服务地址: http://localhost:3001
🔗 健康检查: http://localhost:3001/health
```

### 2. 启动Vue应用

```bash
npm run serve
```

### 3. 测试书籍解析

1. 打开 http://localhost:8080
2. 输入番茄小说链接或书籍ID
3. 点击解析按钮
4. 查看控制台输出

### 4. 验证真实数据

在浏览器控制台中应该看到：
```
🔍 开始获取书籍 7416289834324479001 的真实信息...
🔄 代理请求: /api/reader/directory/detail?bookId=7416289834324479001
✅ 代理成功: /api/reader/directory/detail?bookId=7416289834324479001 -> 200
✅ 成功获取真实章节数据: 156 章
```

## 故障排除

### 1. 代理服务器无法启动

**错误**: `Error: listen EADDRINUSE :::3001`
**解决**: 端口被占用，修改PORT变量或关闭占用端口的程序

```bash
# 查看端口占用
netstat -ano | findstr :3001

# 杀死进程
taskkill /PID <进程ID> /F
```

### 2. 仍然出现CORS错误

**可能原因**:
- 代理服务器未启动
- Vue应用配置错误
- 浏览器缓存问题

**解决方法**:
1. 确认代理服务器正在运行
2. 清除浏览器缓存
3. 检查控制台错误信息

### 3. 获取不到真实数据

**检查步骤**:
1. 访问 http://localhost:3001/health 确认代理服务器正常
2. 直接访问 http://localhost:3001/api/reader/directory/detail?bookId=7416289834324479001
3. 检查网络连接
4. 查看代理服务器控制台日志

### 4. JSON解析失败

**可能原因**:
- 官方API返回格式变化
- 网络问题导致数据不完整

**解决方法**:
1. 检查代理服务器日志
2. 直接访问官方API确认数据格式
3. 更新解析逻辑

## 生产环境部署

### 1. 服务器部署

```bash
# 在服务器上部署代理服务
pm2 start simple-proxy.js --name fanqie-proxy

# 配置Nginx反向代理
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location / {
        root /path/to/vue/dist;
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. 环境变量配置

```javascript
// 生产环境配置
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'https://your-domain.com/api'
```

## 注意事项

1. **合规性**: 确保遵守番茄小说的使用条款
2. **频率限制**: 避免过于频繁的API调用
3. **错误处理**: 完善的错误处理和重试机制
4. **安全性**: 生产环境中保护API密钥和敏感信息
5. **性能**: 考虑添加缓存机制减少API调用

## 成功标志

当看到以下日志时，表示成功获取到真实数据：

```
✅ 成功获取真实章节数据: XXX 章
✅ 官方API获取章节成功: XXX 章
```

此时Vue应用会显示真实的书籍信息和章节列表，而不是模拟数据。
