<template>
  <div class="empty-state">
    <div class="empty-icon">
      <i :class="iconClass"></i>
    </div>
    <h3 class="empty-title">{{ title }}</h3>
    <p class="empty-description">{{ description }}</p>
    <div v-if="showAction" class="empty-action">
      <slot name="action">
        <el-button type="primary" @click="$emit('action')">
          {{ actionText }}
        </el-button>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  
  props: {
    icon: {
      type: String,
      default: 'el-icon-box'
    },
    title: {
      type: String,
      default: '暂无数据'
    },
    description: {
      type: String,
      default: '当前没有任何内容'
    },
    actionText: {
      type: String,
      default: '刷新'
    },
    showAction: {
      type: Boolean,
      default: true
    }
  },
  
  computed: {
    iconClass() {
      return this.icon
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    
    i {
      color: #C0C4CC;
    }
  }
  
  .empty-title {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #606266;
    font-weight: 500;
  }
  
  .empty-description {
    margin: 0 0 30px 0;
    font-size: 14px;
    color: #909399;
    line-height: 1.6;
  }
  
  .empty-action {
    margin-top: 20px;
  }
}
</style>
