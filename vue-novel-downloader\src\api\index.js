import { http, proxyHttp } from '@/utils/http'
import store from '@/store'
import { Message } from 'element-ui'
import { getToken } from '@/utils/auth'

// 导入各个模块的API
import novelAPI from './novel'
import downloadAPI from './download'
import userAPI from './user'

// 开发模式下的模拟数据标志
const isDevelopment = process.env.NODE_ENV === 'development'
const useMockData = isDevelopment && !process.env.VUE_APP_USE_REAL_API

// HTTP请求包装器，添加认证和错误处理
async function apiRequest(url, options = {}) {
  try {
    // 添加认证头
    const headers = { ...options.headers }

    const token = getToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const activationToken = store.getters['user/activationToken']
    if (activationToken) {
      headers['X-Activation-Token'] = activationToken
    }

    // 发送请求
    const response = await http.request(url, { ...options, headers })

    // 开发模式日志
    if (isDevelopment) {
      console.log('API请求:', options.method || 'GET', url, response)
    }

    // 检查业务状态码
    if (response.code && response.code !== 200 && response.code !== '200') {
      Message({
        message: response.message || '请求失败',
        type: 'error',
        duration: 5000
      })

      // 处理认证失败
      if (response.code === 401 || response.code === '401' || response.code === 403 || response.code === '403') {
        store.dispatch('user/logout')
        window.location.href = '#/activation'
      }

      throw new Error(response.message || '请求失败')
    }

    return response

  } catch (error) {
    console.error('API请求错误:', error)

    // 在开发模式下，如果是网络错误，返回模拟数据
    if (useMockData && (error.message.includes('fetch') || error.message.includes('网络'))) {
      console.warn('使用模拟数据，因为后端服务不可用')
      return getMockResponse(url, options)
    }

    let message = '网络错误'
    if (error.message.includes('HTTP')) {
      message = error.message
    } else if (error.message.includes('timeout') || error.message.includes('超时')) {
      message = '请求超时'
    } else {
      message = error.message || '网络连接失败'
    }

    Message({
      message,
      type: 'error',
      duration: 5000
    })

    throw error
  }
}

// 模拟响应数据
function getMockResponse(url, options) {
  // 根据URL返回不同的模拟数据
  if (url.includes('/user/machine-code')) {
    return { code: 200, data: { machineCode: 'MOCK-MACHINE-CODE-12345' } }
  }

  if (url.includes('/user/activation')) {
    return { code: 200, message: '激活成功', data: { token: 'mock-token' } }
  }

  return { code: 200, data: null, message: '模拟响应' }
}






// 从URL中提取token
function extractTokenFromUrl(url) {
  const match = url.match(/tokenName=([^&]+)/)
  return match ? match[1] : ''
}

// 模拟token验证逻辑
function mockTokenVerification(tokenName) {
  // 模拟不同的激活码状态，对应Python版本的返回码
  const mockTokens = {
    // 测试激活码 - 未使用状态
    'TEST123': {
      code: '510',
      msg: '激活码未使用',
      message: '激活码未使用',
      tokenName: tokenName,
      tokenCode: '',
      tokenCompCode: '',
      tokenStatus: '1'
    },

    // 已使用的激活码
    'USED456': {
      code: '500',
      msg: '激活码不存在',
      message: '激活码不存在'
    },

    // 已禁用的激活码
    'DISABLED789': {
      code: '520',
      msg: '激活码已禁用',
      message: '激活码已禁用'
    },

    // 访问频繁
    'FREQUENT999': {
      code: '540',
      msg: '访问频繁，请12小时后重试',
      message: '访问频繁，请12小时后重试'
    },

    // 有效的已绑定激活码
    'VALID888': {
      code: '200',
      tokenCode: tokenName,
      tokenName: tokenName,
      tokenCompCode: 'some_machine_code',
      tokenStatus: '1',
      msg: '激活码有效',
      message: '激活码有效'
    },

    // 默认测试激活码（方便测试）
    'DEMO': {
      code: '510',
      msg: '激活码未使用',
      message: '激活码未使用',
      tokenName: 'DEMO',
      tokenCode: '',
      tokenCompCode: '',
      tokenStatus: '1'
    }
  }

  return mockTokens[tokenName] || {
    code: '500',
    msg: '激活码不存在',
    message: '激活码不存在'
  }
}

// 统一API接口定义 - 使用模块化管理
const api = {
  // 用户相关 - 使用独立的用户API模块
  user: userAPI,

  // 小说相关 - 使用独立的小说API模块
  novel: novelAPI,

  // 下载相关 - 使用独立的下载API模块
  download: downloadAPI,

  // 保留原有的一些通用方法
  _legacy: {
    // 检查激活状态
    checkActivation(token) {
      return apiRequest('/user/check-activation', { method: 'POST', body: { token } })
    },

    // 获取用户信息
    getUserInfo() {
      return apiRequest('/user/info', { method: 'GET' })
    }
  },



  // 系统相关
  system: {
    // 检查更新
    checkUpdate() {
      return apiRequest('/system/check-update', { method: 'GET' })
    },

    // 获取系统信息
    getSystemInfo() {
      return apiRequest('/system/info', { method: 'GET' })
    },

    // 获取配置
    getConfig() {
      return apiRequest('/system/config', { method: 'GET' })
    },

    // 更新配置
    updateConfig(data) {
      return apiRequest('/system/config', { method: 'POST', body: data })
    }
  }
}

export default api
