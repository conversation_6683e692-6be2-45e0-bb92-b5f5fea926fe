@import './variables.scss';

// 全局样式
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: $bg-color;
  color: $text-color-primary;
  line-height: 1.6;
}

// 通用类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { 
  display: flex; 
  flex-direction: column; 
}

// 间距类
.m-0 { margin: 0; }
.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
.mt-4 { margin-top: $spacing-lg; }
.mt-5 { margin-top: $spacing-xl; }

.mb-1 { margin-bottom: $spacing-xs; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-3 { margin-bottom: $spacing-md; }
.mb-4 { margin-bottom: $spacing-lg; }
.mb-5 { margin-bottom: $spacing-xl; }

.ml-1 { margin-left: $spacing-xs; }
.ml-2 { margin-left: $spacing-sm; }
.ml-3 { margin-left: $spacing-md; }
.ml-4 { margin-left: $spacing-lg; }
.ml-5 { margin-left: $spacing-xl; }

.mr-1 { margin-right: $spacing-xs; }
.mr-2 { margin-right: $spacing-sm; }
.mr-3 { margin-right: $spacing-md; }
.mr-4 { margin-right: $spacing-lg; }
.mr-5 { margin-right: $spacing-xl; }

.p-0 { padding: 0; }
.pt-1 { padding-top: $spacing-xs; }
.pt-2 { padding-top: $spacing-sm; }
.pt-3 { padding-top: $spacing-md; }
.pt-4 { padding-top: $spacing-lg; }
.pt-5 { padding-top: $spacing-xl; }

.pb-1 { padding-bottom: $spacing-xs; }
.pb-2 { padding-bottom: $spacing-sm; }
.pb-3 { padding-bottom: $spacing-md; }
.pb-4 { padding-bottom: $spacing-lg; }
.pb-5 { padding-bottom: $spacing-xl; }

.pl-1 { padding-left: $spacing-xs; }
.pl-2 { padding-left: $spacing-sm; }
.pl-3 { padding-left: $spacing-md; }
.pl-4 { padding-left: $spacing-lg; }
.pl-5 { padding-left: $spacing-xl; }

.pr-1 { padding-right: $spacing-xs; }
.pr-2 { padding-right: $spacing-sm; }
.pr-3 { padding-right: $spacing-md; }
.pr-4 { padding-right: $spacing-lg; }
.pr-5 { padding-right: $spacing-xl; }

// 自定义组件样式
.page-container {
  padding: $spacing-lg;
  min-height: calc(100vh - 60px);
}

.card-container {
  background: white;
  border-radius: $border-radius-large;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.button-group {
  display: flex;
  gap: $spacing-sm;
  justify-content: center;
  margin-top: $spacing-lg;
}

// 状态颜色类
.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-info { color: $info-color; }

// 动画类
.fade-enter-active, .fade-leave-active {
  transition: $transition-fade;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: $transition-md-fade;
}
.slide-fade-leave-active {
  transition: $transition-md-fade;
}
.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

// 响应式
@media (max-width: $breakpoint-md) {
  .page-container {
    padding: $spacing-md;
  }
  
  .card-container {
    padding: $spacing-md;
  }
  
  .button-group {
    flex-direction: column;
  }
}

@media (max-width: $breakpoint-sm) {
  .page-container {
    padding: $spacing-sm;
  }
  
  .card-container {
    padding: $spacing-sm;
  }
}
