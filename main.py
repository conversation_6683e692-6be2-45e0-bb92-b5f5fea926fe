import argparse
import sys

from novel_src.constants import VERSION, CLIENT_VERSION
from novel_src.base_system.context import GlobalContext


def main():
    # 参数解析器配置，只保留基本选项
    parser = argparse.ArgumentParser(
        description="Useful Novel Tool - 实用小说工具",
        formatter_class=argparse.RawTextHelpFormatter,
        add_help=False,
    )

    parser.add_argument("--debug", action="store_true", help="启用调试模式（详细输出）")
    parser.add_argument("--version", action="store_true", help="显示程序版本信息")
    parser.add_argument("-h", "--help", action="store_true", help="显示帮助信息并退出")

    # 参数解析
    args = parser.parse_args()

    # 处理系统参数
    if args.help:
        parser.print_help()
        sys.exit(0)

    if args.version:
        print(f"Useful Novel Tool v{VERSION} (客户端版本: {CLIENT_VERSION})")
        sys.exit(0)

    # 启动GUI模式
    debug_mode = args.debug
    from novel_src.gui.app import start_gui
    sys.exit(start_gui(debug_mode))


if __name__ == "__main__":
    main()
