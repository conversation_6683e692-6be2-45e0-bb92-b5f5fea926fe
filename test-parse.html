<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书籍信息解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        button:hover {
            opacity: 0.9;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .book-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .book-info h3 {
            margin-top: 0;
            color: #495057;
        }

        .book-info p {
            margin: 8px 0;
        }

        .book-info strong {
            color: #343a40;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>书籍信息解析测试</h1>
        <p>测试新增的解析书籍信息功能</p>

        <div class="input-group">
            <label for="novelUrl">小说链接或ID：</label>
            <input type="text" id="novelUrl"
                placeholder="例如：https://fanqienovel.com/page/7512373964816010265 或 7512373964816010265">
        </div>

        <button onclick="parseBookInfo()" id="parseBtn">解析书籍信息</button>
        <button onclick="testProxy()" id="testBtn">测试代理服务器</button>

        <div id="result" class="result"></div>
    </div>

    <script>
        // 测试代理服务器连接
        async function testProxy() {
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');

            try {
                testBtn.disabled = true;
                resultDiv.className = 'result loading';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在测试代理服务器连接...';

                const response = await fetch('http://localhost:3001/health');
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>代理服务器连接成功</h3>
                    <p><strong>状态：</strong>${data.status}</p>
                    <p><strong>时间：</strong>${new Date(data.timestamp).toLocaleString()}</p>
                    <p><strong>端口：</strong>${data.port || 3001}</p>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>代理服务器连接失败</h3>
                    <p><strong>错误：</strong>${error.message}</p>
                    <p>请确保代理服务器正在运行（端口3001）</p>
                    <p>运行命令：<code>node proxy-server.js</code> 或 <code>start-proxy.bat</code></p>
                `;
            } finally {
                testBtn.disabled = false;
            }
        }

        // 解析书籍信息
        async function parseBookInfo() {
            const novelUrl = document.getElementById('novelUrl').value.trim();
            const resultDiv = document.getElementById('result');
            const parseBtn = document.getElementById('parseBtn');

            if (!novelUrl) {
                alert('请输入小说链接或ID');
                return;
            }

            try {
                parseBtn.disabled = true;
                resultDiv.className = 'result loading';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在解析书籍信息，请稍候...';

                // 提取书籍ID
                const bookId = extractBookId(novelUrl);
                if (!bookId) {
                    throw new Error('无效的小说链接格式');
                }

                // 构建请求URL
                const pageUrl = `https://fanqienovel.com/page/${bookId}?enter_from=stack-room`;

                // 通过代理获取页面内容
                const response = await fetch('http://localhost:3001/proxy/fetch-page', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url: pageUrl })
                });

                const result = await response.json();

                if (result.success) {
                    // 解析HTML内容
                    const bookInfo = parseBookInfoFromHtml(result.html, bookId);

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>解析成功</h3>
                        <div class="book-info">
                            <h3>《${bookInfo.novelName}》</h3>
                            <p><strong>作者：</strong>${bookInfo.author}</p>
                            <p><strong>简介：</strong>${bookInfo.description}</p>
                            <p><strong>小说ID：</strong>${bookInfo.novelId}</p>
                            <p><strong>解析时间：</strong>${new Date(bookInfo.parseTime).toLocaleString()}</p>
                        </div>
                    `;
                } else {
                    throw new Error(result.error || '获取页面内容失败');
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>解析失败</h3>
                    <p><strong>错误：</strong>${error.message}</p>
                `;
            } finally {
                parseBtn.disabled = false;
            }
        }

        // 提取书籍ID
        function extractBookId(url) {
            if (!url) return null;

            // 检查是否为纯数字ID
            if (/^\d+$/.test(url.trim())) {
                return url.trim();
            }

            // 支持多种URL格式
            const patterns = [
                /\/page\/(\d+)/,
                /\/reader\/(\d+)/,
                /[?&]book_id=(\d+)/,
                /[?&]bookId=(\d+)/,
                /(\d{19})/
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }

            return null;
        }

        // 从HTML中解析书籍信息
        function parseBookInfoFromHtml(html, novelId) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 提取小说名称
            let novelName = '';
            const titleSelectors = [
                'h1[data-testid="book-title"]',
                '.book-title',
                'h1.title',
                '.novel-title',
                'title'
            ];

            for (const selector of titleSelectors) {
                const element = doc.querySelector(selector);
                if (element && element.textContent.trim()) {
                    novelName = element.textContent.trim();
                    if (selector === 'title') {
                        novelName = novelName.replace(/\s*-\s*番茄小说.*$/, '').trim();
                    }
                    break;
                }
            }

            // 提取作者信息 - 精准匹配author-name-text
            let author = '';
            const authorDiv = doc.querySelector('div.author-name');
            if (authorDiv) {
                const authorSpan = authorDiv.querySelector('span.author-name-text');
                if (authorSpan && authorSpan.textContent.trim()) {
                    author = authorSpan.textContent.trim();
                }
            }

            // 如果没有找到，尝试其他选择器
            if (!author) {
                const authorSelectors = [
                    '[data-testid="book-author"]',
                    '.book-author',
                    '.author',
                    '.novel-author'
                ];

                for (const selector of authorSelectors) {
                    const element = doc.querySelector(selector);
                    if (element && element.textContent.trim()) {
                        author = element.textContent.trim();
                        author = author.replace(/^作者[：:]\s*/, '').replace(/\s*著$/, '').trim();
                        break;
                    }
                }
            }

            // 精准提取简介信息 - 优先使用page-abstract-content
            let description = '';
            const descDiv = doc.querySelector('div.page-abstract-content');
            if (descDiv) {
                const descP = descDiv.querySelector('p');
                if (descP && descP.textContent.trim()) {
                    description = descP.textContent.trim();
                } else {
                    // 如果p标签为空，尝试获取div的直接文本内容
                    const directText = descDiv.textContent.trim();
                    if (directText) {
                        description = directText;
                    }
                }
            }

            // 如果page-abstract-content没有找到，尝试其他选择器
            if (!description) {
                const descSelectors = [
                    '[data-testid="book-intro"]',
                    '.book-intro',
                    '.book-description',
                    '.novel-desc',
                    '.intro',
                    '.description'
                ];

                for (const selector of descSelectors) {
                    const element = doc.querySelector(selector);
                    if (element && element.textContent.trim()) {
                        description = element.textContent.trim();
                        break;
                    }
                }
            }

            // 提取章节数
            let totalChapters = 0;
            const pagesHeaderDiv = doc.querySelector('div.page-directory-header');
            if (pagesHeaderDiv) {
                const h3Element = pagesHeaderDiv.querySelector('h3');
                if (h3Element) {
                    const rawText = h3Element.textContent.trim();
                    const chapterMatch = rawText.match(/\d+/);
                    if (chapterMatch) {
                        totalChapters = parseInt(chapterMatch[0]);
                    }
                }
            }

            // 如果没有找到信息，使用默认值
            if (!novelName) novelName = `小说_${novelId}`;
            if (!author) author = '未知作者';
            if (!description) description = '暂无简介';

            return {
                novelId,
                novelName,
                author,
                description,
                parseTime: new Date().toISOString()
            };
        }
    </script>
</body>

</html>