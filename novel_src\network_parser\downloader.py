# -------------------------------
# downloader.py - 核心下载模块
# 职责：实现多线程下载和任务管理
# -------------------------------
import re
import time
import json
import random
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from typing import List, Dict, Optional, Tuple

from .network import NetworkClient
# 不再需要下载旧版API相关功能
# from .visit_model import download_chapter_official, get_a_iid
from .visit_model import get_a_iid
from ..book_parser.book_manager import BookManager
from ..book_parser.parser import ContentParser
from ..base_system.context import GlobalContext
from ..base_system.log_system import TqdmLoggingHandler
# 移除对fanqie_api的引用
# from .fanqie_api import get_chapter_content, format_chapter_content
# 使用NovelServerAPI替代
from .novel_server_api import NovelServerAPI


class APIManager:
    def __init__(self, api_endpoints, config, network_status):
        self.api_queue = queue.Queue()
        self.config = config
        self.network_status = network_status  # 存储各个 API 的状态信息，例如 failure_count、response_time、cooldown_until 等
        for ep in api_endpoints:
            self.api_queue.put(ep)

    def get_api(self, timeout=1.0):
        """从队列中获取一个可用的 API 端点，如果队列为空则等待"""
        while True:
            try:
                ep = self.api_queue.get(timeout=timeout)
            except queue.Empty:
                # 队列暂时为空，等待后重试
                time.sleep(0.05)
                continue

            # 检查该 API 是否处于冷却状态
            cooldown = self.network_status.get(ep, {}).get("cooldown_until", 0)
            if time.time() < cooldown:
                # 当前 API 尚未冷却结束，放回队列后再取下一个
                self.api_queue.put(ep)
                time.sleep(0.05)
                continue
            return ep

    def release_api(self, ep):
        """任务结束后，把 API 放回队列"""
        self.api_queue.put(ep)


class ChapterDownloader:
    """章节下载调度器"""

    def __init__(self, book_id: str, network_client: NetworkClient):
        self.book_id = book_id
        self.network = network_client
        self.headers = self.network.get_headers()
        self.logger = GlobalContext.get_logger()
        self.log_system = GlobalContext.get_log_system()
        self.config = GlobalContext.get_config()
        # 初始化NovelServerAPI
        self.server_api = NovelServerAPI()

        # 使用线程安全的事件对象来控制中断
        self._stop_event = threading.Event()

        # 设置进度回调
        self.progress_callback = None

        self.api_manager = APIManager(
            api_endpoints=self.config.api_endpoints,
            config=self.config,
            network_status=self.network._api_status,
        )

    def request_stop(self):
        """请求停止下载（线程安全）"""
        self.logger.warning("正在尝试优雅退出...")
        self._stop_event.set()

    def fetch_chapter_list(self) -> Optional[List[Dict]]:
        """从API获取章节列表"""
        api_url = (
            f"https://fanqienovel.com/api/reader/directory/detail?bookId={self.book_id}"
        )
        try:
            self.logger.debug(f"开始获取章节列表，URL: {api_url}")
            response = self.network.session.get(
                api_url, headers=self.headers, timeout=self.config.request_timeout
            )
            self.logger.debug(
                f"章节列表响应状态: {response.status_code} 长度: {len(response.text)}字节"
            )

            response.raise_for_status()
            result = self._parse_chapter_data(response.json())

            # 打印解析结果的简要信息
            if result:
                print(f"成功解析章节列表，共{len(result)}章，前3章标题: {[ch['title'] for ch in result[:3]]}")
            else:
                print("解析章节列表失败，返回为空")

            return result
        except Exception as e:
            self.logger.error(f"获取章节列表失败: {str(e)}", exc_info=True)
            if "response" in locals():
                self.logger.debug(f"错误响应内容: {response.text[:200]}...")
            return None

    def _parse_chapter_data(self, response_data: dict) -> List[Dict]:
        """解析章节API响应"""
        self.logger.debug(f"开始解析章节数据，响应码: {response_data.get('code')}")

        if response_data.get("code") != 0:
            self.logger.error(
                f"API错误数据: {json.dumps(response_data, ensure_ascii=False)[:200]}..."
            )
            raise ValueError(f"API错误: {response_data.get('message')}")

        # 获取章节ID列表
        chapters = response_data["data"]["allItemIds"]

        # 尝试从chapterListWithVolume中获取章节标题
        try:
            chapter_info_map = {}

            # 从chapterListWithVolume中获取章节信息
            chapter_lists = response_data["data"].get("chapterListWithVolume", [])
            if chapter_lists and len(chapter_lists) > 0:
                # chapterListWithVolume是一个二维数组，第一层是卷，第二层是章节
                for volume_chapters in chapter_lists:
                    for chapter in volume_chapters:
                        item_id = chapter.get("itemId")
                        if item_id:
                            chapter_info_map[item_id] = {
                                "title": chapter.get("title", ""),
                                "index": chapter.get("needPay", 0)  # 使用needPay作为索引，实际使用时可能需要调整
                            }

            self.logger.info(f"成功从API获取到{len(chapter_info_map)}个章节标题信息")
        except Exception as e:
            self.logger.warning(f"获取章节标题时出错: {str(e)}")
            chapter_info_map = {}

        # 构建章节信息列表
        result = []
        for idx, chapter_id in enumerate(chapters):
            # 尝试从映射中获取章节标题，如果没有则使用默认标题
            if chapter_id in chapter_info_map:
                title = chapter_info_map[chapter_id].get("title")
                index = chapter_info_map[chapter_id].get("index", idx)
            else:
                title = f"第{idx + 1}章"
                index = idx

            result.append({
                "id": chapter_id,
                "title": title,
                "index": index
            })

        # 打印一些章节标题示例，用于调试
        title_examples = [f"{ch['id'][:8]}...: {ch['title']}" for ch in result[:5]]
        self.logger.info(f"解析到{len(chapters)}个章节，标题示例: {title_examples}")
        return result

    def download_book(
            self, book_manager: BookManager, book_name: str, chapters: List[Dict]
    ) -> Dict[str, int]:
        """执行多线程下载任务"""
        if self.config.use_official_api and not self.config.iid:
            get_a_iid()
        original_handlers = self.logger.handlers.copy()

        # 临时关闭非tqdm处理器
        for handler in original_handlers:
            if not isinstance(handler, TqdmLoggingHandler):
                self.logger.removeHandler(handler)
        results = {"success": 0, "failed": 0, "canceled": 0}

        try:
            max_workers = (
                min(self.config.max_workers, len(self.config.api_endpoints))
                if not self.config.use_official_api
                else self.config.max_workers
            )
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 创建未来任务列表
                futures = {
                    executor.submit(
                        (
                            self._download_single
                            if not self.config.use_official_api
                            else self._download_single_official
                        ),
                        ch,
                    ): ch
                    for ch in chapters
                    if (
                            ch["id"] not in book_manager.downloaded
                            or book_manager.downloaded.get(ch["id"]) == [ch["id"], "Error"]
                    )
                }

                # 使用可中断的进度条
                with tqdm(total=len(futures), desc=f"下载《{book_name}》") as progress:
                    self.log_system.enable_tqdm_handler(progress)
                    completed_count = 0
                    total_count = len(futures)

                    # 初始化进度通知
                    if self.progress_callback:
                        self.progress_callback(0, total_count, f"开始下载《{book_name}》")

                    for future in as_completed(futures):
                        if self._stop_event.is_set():
                            self._cancel_pending(futures)
                            results["canceled"] = len(futures) - completed_count
                            break

                        ch = futures[future]
                        try:
                            content, title = future.result()
                            completed_count += 1

                            if content == "Error":
                                book_manager.save_error_chapter(title, chapters[title]["title"])
                                results["failed"] += 1
                            else:
                                book_manager.save_chapter(
                                    ch, title, content
                                )  # 统一保存入口
                                results["success"] += 1

                            # 更新进度回调
                            if self.progress_callback:
                                message = f"下载中... 成功:{results['success']} 失败:{results['failed']} 总计:{total_count}"
                                # 如果回调返回False，表示取消下载
                                if not self.progress_callback(completed_count, total_count, message):
                                    self._stop_event.set()
                                    self._cancel_pending(futures)
                                    results["canceled"] = len(futures) - completed_count
                                    break

                        except KeyboardInterrupt:
                            book_manager.save_download_status()
                            raise
                        except Exception as e:
                            self.logger.error(f"章节下载出错: {str(e)}")
                            results["failed"] += 1
                            completed_count += 1

                            # 更新错误进度
                            if self.progress_callback:
                                message = f"下载中... 成功:{results['success']} 失败:{results['failed']} 总计:{total_count}"
                                self.progress_callback(completed_count, total_count, message)

                        progress.update()
                    book_manager.save_download_status()
                    book_manager.finalize_spawn(
                        chapters, len(futures) - results["success"]
                    )

        except KeyboardInterrupt:
            self.logger.warning("用户主动中断下载")
            results["canceled"] = len(futures) - results["success"] - results["failed"]
            book_manager.save_download_status()

        finally:
            # 确保最后一次调用进度回调
            if self.progress_callback and 'total_count' in locals():
                self.progress_callback(
                    total_count - results["canceled"],
                    total_count,
                    f"下载完成: 成功:{results['success']} 失败:{results['failed']} 取消:{results['canceled']}"
                )

            # 确保移除临时处理器
            self.log_system.disable_tqdm_handler()
            # 恢复原有处理器
            for handler in original_handlers:
                self.logger.addHandler(handler)

        return results

    def _cancel_pending(self, futures):
        """取消未完成的任务"""
        for future in futures:
            if not future.done():
                future.cancel()

    def _download_single(self, chapter: dict) -> Tuple[str, str]:
        chapter_id = chapter["id"]
        retry_count = 0
        request_id = f"{chapter_id[:4]}-{random.randint(1000, 9999)}"

        self.logger.debug(f"[{request_id}] 开始下载章节 {chapter['title']}")

        while retry_count < self.config.max_retries:
            if self._stop_event.is_set():
                self.logger.debug("检测到停止信号，中止下载")
                return "Error", chapter_id

            try:
                # 添加随机延迟（50-300ms）
                delay = random.randint(
                    self.config.min_wait_time, self.config.max_wait_time
                )
                self.logger.debug(f"[{request_id}] 添加随机延迟: {delay}ms")
                time.sleep(delay / 1000)

                start_time = time.time()

                # 使用激活码获取章节内容
                token = GlobalContext.get_token()
                if not token:
                    self.logger.error(f"[{request_id}] 未找到有效的激活码")
                    return "Error", chapter_id

                # 使用服务器API获取单章节内容
                # 注意：实际上已经没有单章节获取接口，这里保留是为了结构完整性
                self.logger.warning(f"[{request_id}] 单章节下载方式已不再支持，请使用批量下载")
                return "Error", chapter_id

            except Exception as e:
                self.logger.error(f"[{request_id}] 下载发生异常: {str(e)}")
                retry_count += 1

            # 指数退避等待后重试
            backoff_time = 0.5 * (2 ** retry_count)
            self.logger.debug(f"[{request_id}] 等待 {backoff_time:.1f}s 后重试")
            time.sleep(backoff_time)

        self.logger.error(f"[{request_id}] 下载失败，多次重试均未成功")
        return "Error", chapter_id

    def _download_single_official(self, chapter: dict) -> Tuple[str, str]:
        """使用新的API下载官方章节内容"""
        chapter_id = chapter["id"]
        request_id = f"{chapter_id[:4]}-{random.randint(1000, 9999)}"

        self.logger.debug(f"[{request_id}] 开始下载章节 {chapter['title']}")

        try:
            # 保留随机延迟（50-300ms）
            delay = random.randint(
                self.config.min_wait_time, self.config.max_wait_time
            )
            self.logger.debug(f"[{request_id}] 添加随机延迟: {delay}ms")
            time.sleep(delay / 1000)

            # 提示不再支持单章节下载
            self.logger.warning(f"[{request_id}] 单章节下载方式已不再支持，请使用批量下载")
            return "Error", chapter_id

        except Exception as e:
            self.logger.error(f"[{request_id}] 下载失败，原因：{e}")
            return "Error", chapter_id


# 添加测试函数
def test_chapter_parsing(book_id, json_data=None):
    """测试章节标题解析功能

    Args:
        book_id: 书籍ID
        json_data: 可选，直接提供的JSON数据字符串
    """
    from ..base_system.context import GlobalContext
    network = GlobalContext.get_network_client()
    downloader = ChapterDownloader(book_id, network)

    if json_data:
        # 如果提供了JSON数据，直接解析
        import json
        data = json.loads(json_data)
        chapters = downloader._parse_chapter_data(data)
    else:
        # 否则从API获取
        chapters = downloader.fetch_chapter_list()
        if chapters:
            for i, ch in enumerate(chapters[:10]):  # 只打印前10章
                print(f"{i + 1}. {ch['title']} (ID: {ch['id'][:8]}...)")
        else:
            print("获取章节列表失败")

    return chapters
