# 格式选择功能使用指南

## 功能概述
现在软件支持在下载小说时选择输出格式，用户可以选择TXT文本格式或EPUB电子书格式。

## 使用方法

### 某茄小说下载
1. 在"某茄小说"标签页中，输入小说ID或链接
2. 选择保存路径
3. 在"保存格式"下拉框中选择：
   - **TXT 文本格式**：生成纯文本文件，适合阅读软件或文本编辑器
   - **EPUB 电子书格式**：生成标准电子书格式，适合电子书阅读器
4. 点击"开始下载"

### 某猫小说下载
1. 在"某猫小说"标签页中，输入小说ID或链接（或使用搜索功能）
2. 选择保存路径
3. 在"保存格式"下拉框中选择：
   - **TXT 文本格式**：生成纯文本文件
   - **EPUB 电子书格式**：生成电子书文件
4. 点击"开始下载"

## 格式特点

### TXT格式
- **优点**：
  - 文件小，占用空间少
  - 兼容性好，任何文本编辑器都能打开
  - 加载速度快
- **适用场景**：
  - 需要编辑文本内容
  - 存储空间有限
  - 使用简单的阅读软件

### EPUB格式
- **优点**：
  - 标准电子书格式，支持章节导航
  - 支持封面、目录、元数据
  - 适合电子书阅读器
  - 排版美观，阅读体验好
- **适用场景**：
  - 使用专业电子书阅读器
  - 需要良好的阅读体验
  - 收藏电子书

## 技术说明

### 某茄小说
- 选择TXT格式：服务器直接返回TXT文件
- 选择EPUB格式：
  - 如果服务器支持，直接返回EPUB文件
  - 如果服务器不支持，客户端自动将TXT转换为EPUB

### 某猫小说
- 选择TXT格式：本地处理生成TXT文件
- 选择EPUB格式：本地处理生成EPUB文件，不生成TXT文件

## 注意事项

1. **网络连接**：某茄小说需要网络连接到服务器
2. **存储空间**：EPUB文件通常比TXT文件稍大
3. **转换时间**：TXT转EPUB可能需要额外的处理时间
4. **依赖库**：EPUB生成需要ebooklib库支持

## 常见问题

### Q: 为什么选择EPUB格式后下载时间更长？
A: EPUB格式需要额外的处理步骤，包括章节分割、目录生成、样式设置等，所以会比TXT格式耗时更长。

### Q: 生成的EPUB文件可以在哪些设备上阅读？
A: EPUB是标准格式，支持大多数电子书阅读器，包括：
- Kindle（需转换为MOBI格式）
- 苹果Books
- Google Play Books
- Adobe Digital Editions
- 各种手机阅读APP

### Q: 如果EPUB生成失败怎么办？
A: 如果EPUB生成失败，系统会自动保留TXT文件作为备份，确保下载的内容不会丢失。

### Q: 可以同时生成TXT和EPUB两种格式吗？
A: 目前不支持同时生成两种格式。如果需要两种格式，请分别下载两次。

## 配置保存
软件会记住您上次选择的格式，下次启动时会自动选择相同的格式。

## 更新说明
- 新增格式选择功能
- 优化文件处理逻辑
- 改进EPUB生成质量
- 支持服务器端格式选择

如有问题或建议，请联系开发者。
