#!/bin/bash

# 某茄小说下载器 - 自动打包工具 (Linux/macOS)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        print_error "$1 未找到"
        return 1
    fi
    return 0
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! check_command python3; then
        print_error "未找到Python3，请先安装Python 3.7+"
        exit 1
    fi
    
    # 检查Python版本
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_success "Python版本: $python_version"
    
    # 检查pip
    if ! check_command pip3; then
        print_error "未找到pip3"
        exit 1
    fi
    
    print_success "Python环境检查通过"
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖包..."
    
    print_info "安装PyInstaller..."
    pip3 install pyinstaller
    
    print_info "安装项目依赖..."
    pip3 install -r requirements.txt
    
    print_success "依赖包安装完成"
}

# 快速构建
quick_build() {
    print_info "开始快速构建..."
    python3 quick_build.py
}

# 完整构建
full_build() {
    print_info "开始完整构建..."
    python3 build_exe.py
}

# 显示菜单
show_menu() {
    echo
    echo "========================================"
    echo "   某茄小说下载器 - 自动打包工具"
    echo "========================================"
    echo
    echo "请选择构建模式:"
    echo "[1] 快速构建 (开发测试用)"
    echo "[2] 完整构建 (发布用)"
    echo "[3] 安装依赖包"
    echo "[4] 退出"
    echo
}

# 主函数
main() {
    # 检查Python环境
    check_python
    
    while true; do
        show_menu
        read -p "请输入选择 (1-4): " choice
        
        case $choice in
            1)
                echo
                quick_build
                break
                ;;
            2)
                echo
                full_build
                break
                ;;
            3)
                echo
                install_dependencies
                echo
                read -p "按回车键继续..."
                ;;
            4)
                echo
                print_info "再见！"
                exit 0
                ;;
            *)
                echo
                print_error "无效选择，请输入1-4"
                echo
                ;;
        esac
    done
    
    echo
    read -p "按回车键退出..."
}

# 运行主函数
main "$@"
