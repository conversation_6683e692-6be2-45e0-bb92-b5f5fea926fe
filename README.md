# Useful Novel Tool

## 项目介绍

Useful Novel Tool 是一款功能强大的小说下载工具，支持番茄小说和七猫小说平台。该工具采用现代化的PyQt5界面设计，支持异步下载，导出为TXT和EPUB格式。通过调用后端API进行小说内容的获取和处理，支持任务状态查询和文件下载功能。

## 主要功能

1. **异步下载**: 将下载任务提交到后端服务器，异步处理小说内容
2. **任务状态查询**: 实时查询下载任务的处理状态和进度
3. **格式支持**: 支持TXT和EPUB两种格式的小说文件
4. **用户激活**: 通过激活码系统控制用户权限和使用次数
5. **自动更新**: 支持软件版本检查和自动更新

## 系统要求

- 操作系统: Windows 7/10/11, macOS, Linux
- Python版本: 3.7+
- 依赖库: PyQt5, requests, beautifulsoup4等

## 安装方法

### 方法1: 使用可执行文件

从发布页面下载最新的可执行文件，直接运行即可。

### 方法2: 从源码安装

1. 克隆仓库:
```bash
git clone https://github.com/yourusername/fq_novel.git
cd fq_novel
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. 运行程序:
```bash
python main.py
```

## 使用说明

1. **激活软件**: 首次运行时需要输入有效的激活码
2. **下载小说**: 
   - 输入番茄小说ID或链接
   - 选择保存路径和格式
   - 点击"开始下载"按钮
3. **查看任务状态**: 程序会自动查询任务状态并显示进度
4. **下载文件**: 任务完成后可以将文件下载到本地

## 技术架构

本项目采用前后端分离架构:

1. **前端**: Python + PyQt5实现的桌面客户端
2. **后端**: Java Spring Boot服务，负责小说内容的获取、处理和存储
3. **存储**: 使用Redis缓存下载任务状态，Minio存储小说文件

## 更新日志

### v1.5.0
- 重构下载流程，支持异步下载
- 添加任务状态查询功能
- 支持从服务器下载处理好的文件
- 优化用户界面和交互体验

### v1.4.0
- 初始版本

## 注意事项

- 本软件仅供学习和研究使用，请勿用于任何商业用途
- 请遵守相关法律法规，尊重版权所有者的权益
- 激活码有使用次数限制，请合理使用

## 许可证

本项目采用 MIT 许可证 