import Vue from 'vue'
import Vuex from 'vuex'

// 模块
import app from './modules/app'
import user from './modules/user'
import settings from './modules/settings'
import download from './modules/download'
import tasks from './modules/tasks'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    settings,
    download,
    tasks
  },
  
  state: {
    // 全局状态
  },
  
  mutations: {
    // 全局mutations
  },
  
  actions: {
    // 全局actions
  },
  
  getters: {
    // 全局getters
  }
})

export default store
