# Vue下载器使用示例

## 基本下载流程

### 1. 用户激活
```javascript
// 在激活页面
const activationResult = await this.$store.dispatch('user/activate', {
  token: 'YOUR_ACTIVATION_TOKEN'
})

if (activationResult.success) {
  console.log('激活成功')
  // 跳转到下载页面
  this.$router.push('/download')
}
```

### 2. 解析小说信息
```javascript
// 在下载页面
async parseNovelUrl() {
  const bookId = this.$fanqieParser.extractBookId(this.form.novelUrl)
  const completeInfo = await this.$fanqieParser.getCompleteBookInfo(bookId)
  
  if (completeInfo.success) {
    this.novelInfo = {
      novelId: bookId,
      novelName: completeInfo.bookInfo.bookName,
      novelAuthor: completeInfo.bookInfo.author,
      novelDesc: completeInfo.bookInfo.description,
      totalChapters: completeInfo.chapters.length
    }
    this.chapterList = completeInfo.chapters
  }
}
```

### 3. 开始下载
```javascript
async startDownload() {
  // 设置下载信息
  await this.setCurrentDownload({
    platform: 'fanqie',
    novelId: this.novelInfo.novelId,
    novelName: this.novelInfo.novelName,
    novelAuthor: this.novelInfo.novelAuthor,
    novelDesc: this.novelInfo.novelDesc,
    selectedChapters: this.chapterList.map(ch => ch.id),
    chapterTitles: this.chapterList.map(ch => ch.title),
    format: this.form.format
  })

  // 开始下载
  const result = await this.startDownloadAction()
  if (result.success) {
    this.$message.success('下载已开始')
  }
}
```

### 4. 监听下载进度
```javascript
// 在组件中监听下载状态变化
computed: {
  ...mapGetters({
    downloadStatus: 'download/downloadStatus'
  })
},

watch: {
  'downloadStatus.progress'(newProgress) {
    if (newProgress >= 100) {
      this.$message.success('下载完成！')
    }
  },
  
  'downloadStatus.downloadCompleted'(completed) {
    if (completed && this.downloadStatus.fileUrl) {
      // 自动下载文件或显示下载按钮
      this.showDownloadButton = true
    }
  }
}
```

### 5. 下载完成后的文件处理
```javascript
async downloadCompletedFile() {
  const fileName = `${this.novelInfo.novelName}.${this.form.format}`
  const success = await this.downloadFileAction({
    fileUrl: this.downloadStatus.fileUrl,
    fileName: fileName,
    formatType: this.form.format
  })
  
  if (success) {
    this.$message.success('文件下载已开始')
  }
}
```

## 高级功能

### 1. 检查用户额度
```javascript
async checkUserQuota() {
  const result = await this.$store.dispatch('user/getUserQuota')
  if (result.success) {
    console.log('剩余额度:', result.quota.remainingQuota)
    return result.quota
  }
}
```

### 2. 错误处理
```javascript
// 在下载过程中处理错误
watch: {
  'downloadStatus.status'(newStatus) {
    if (newStatus === 'failed') {
      this.$message.error('下载失败: ' + this.downloadStatus.errorMessage)
      // 显示重试按钮
      this.showRetryButton = true
    }
  }
}

// 重试下载
async retryDownload() {
  try {
    this.resetForm()
    await this.startDownload()
  } catch (error) {
    this.$message.error('重试失败: ' + error.message)
  }
}
```

### 3. 批量操作
```javascript
// 批量下载多本小说
async batchDownload(novelList) {
  for (const novel of novelList) {
    try {
      await this.downloadSingleNovel(novel)
      // 等待一段时间避免请求过于频繁
      await this.sleep(2000)
    } catch (error) {
      console.error(`下载 ${novel.name} 失败:`, error)
    }
  }
}

sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
```

## 状态管理示例

### 1. 在组件中使用Store
```javascript
export default {
  computed: {
    ...mapGetters({
      isActivated: 'user/isActivated',
      downloadStatus: 'download/downloadStatus',
      remainingQuota: 'user/remainingQuota'
    })
  },
  
  methods: {
    ...mapActions({
      startDownload: 'download/startDownload',
      stopDownload: 'download/stopDownload',
      getUserQuota: 'user/getUserQuota'
    })
  }
}
```

### 2. 直接调用Store方法
```javascript
// 在任何组件中
async someMethod() {
  // 获取状态
  const isDownloading = this.$store.getters['download/isDownloading']
  
  // 调用action
  await this.$store.dispatch('download/startDownload')
  
  // 提交mutation
  this.$store.commit('download/UPDATE_PROGRESS', {
    progress: 50,
    currentChapter: '第25章'
  })
}
```

## API调用示例

### 1. 直接调用API
```javascript
import api from '@/api'

// 请求下载
const result = await api.download.requestNovelDownload({
  token: 'YOUR_TOKEN',
  novelId: '123456789',
  novelName: '测试小说',
  novelAuthor: '测试作者',
  novelDesc: '测试描述',
  format: 'txt',
  chapterIds: ['ch1', 'ch2'],
  chapterTitles: ['第一章', '第二章']
})

// 查询任务状态
const status = await api.download.checkDownloadTask('task_123')

// 获取用户额度
const quota = await api.download.getUserQuota('YOUR_TOKEN')
```

### 2. 错误处理
```javascript
try {
  const result = await api.download.requestNovelDownload(data)
  if (result.code === 200) {
    console.log('请求成功')
  } else {
    throw new Error(result.msg || '请求失败')
  }
} catch (error) {
  console.error('API调用失败:', error)
  this.$message.error('操作失败: ' + error.message)
}
```

## 最佳实践

1. **总是检查用户激活状态**
2. **在下载前检查用户额度**
3. **提供清晰的进度反馈**
4. **处理网络错误和重试机制**
5. **保存用户设置到本地存储**
6. **提供友好的错误提示**
