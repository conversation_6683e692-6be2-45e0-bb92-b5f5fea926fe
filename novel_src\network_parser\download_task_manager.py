"""
download_task_manager.py - 下载任务管理模块
负责管理异步下载任务和任务状态查询
"""

import time
import threading
import requests
import os
from typing import Dict, Any, Optional, Callable

# 移除顶层导入，避免循环导入
# from ..base_system.context import GlobalContext
from .novel_server_api import NovelServerAPI


class DownloadTaskStatus:
    """下载任务状态常量"""
    PENDING = "pending"       # 等待中
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 完成
    FAILED = "failed"         # 失败
    CANCELED = "canceled"     # 取消


class DownloadTask:
    """下载任务类"""

    def __init__(self, task_id: str, novel_id: str, novel_name: str):
        """
        初始化下载任务

        Args:
            task_id: 任务ID
            novel_id: 小说ID
            novel_name: 小说名称
        """
        self.task_id = task_id
        self.novel_id = novel_id
        self.novel_name = novel_name
        self.status = DownloadTaskStatus.PENDING
        self.progress = 0
        self.file_url = None
        self.error_message = None
        self.create_time = time.time()
        self.update_time = time.time()
        # 添加后端任务ID
        self.backend_task_id = None

    def update_status(self, status: str, progress: int = None, file_url: str = None, error_message: str = None):
        """
        更新任务状态

        Args:
            status: 任务状态
            progress: 进度百分比
            file_url: 文件URL
            error_message: 错误信息
        """
        self.status = status
        if progress is not None:
            self.progress = progress
        if file_url:
            self.file_url = file_url
        if error_message:
            self.error_message = error_message
        self.update_time = time.time()


class DownloadTaskManager:
    """下载任务管理器"""

    def __init__(self):
        """初始化下载任务管理器"""
        # 延迟导入GlobalContext，避免循环导入
        from ..base_system.context import GlobalContext

        self.logger = GlobalContext.get_logger()
        self.config = GlobalContext.get_config()
        self.api = NovelServerAPI()
        self.tasks = {}  # 任务字典，键为novel_id
        self.task_threads = {}  # 任务线程字典
        self.task_callbacks = {}  # 任务回调函数字典

    def start_download_task(self, token: str, novel_id: str, novel_name: str,
                           novel_author: str, novel_desc: str, chapter_ids=None, chapter_titles=None,
                           format_choice: str = "txt", on_status_change: Callable[[Dict[str, Any]], None] = None) -> Dict[str, Any]:
        """
        启动下载任务

        Args:
            token: 激活码
            novel_id: 小说ID
            novel_name: 小说名称
            novel_author: 小说作者
            novel_desc: 小说描述
            chapter_ids: 章节ID列表
            chapter_titles: 章节标题列表
            format_choice: 用户选择的文件格式 (txt/epub)
            on_status_change: 状态变更回调函数

        Returns:
            任务信息字典
        """
        # 检查是否已存在相同的任务
        if novel_id in self.tasks:
            task = self.tasks[novel_id]
            # 如果任务已完成或失败，则创建新任务
            if task.status in [DownloadTaskStatus.COMPLETED, DownloadTaskStatus.FAILED]:
                self.logger.info(f"重新创建已完成或失败的下载任务: {novel_name} (ID: {novel_id})")
            else:
                self.logger.info(f"任务已存在: {novel_name} (ID: {novel_id}), 状态: {task.status}")
                return {
                    "task_id": task.task_id,
                    "novel_id": novel_id,
                    "novel_name": novel_name,
                    "status": task.status,
                    "progress": task.progress,
                    "file_url": task.file_url,
                    "error_message": task.error_message
                }

        # 创建新任务
        task_id = f"task_{int(time.time())}_{novel_id}"
        task = DownloadTask(task_id, novel_id, novel_name)
        self.tasks[novel_id] = task

        # 保存回调函数
        if on_status_change:
            self.task_callbacks[novel_id] = on_status_change

        # 直接使用传入的chapter_titles参数，不再尝试从chapter_ids中提取
        # 这样可以确保正确使用前端传入的章节标题

        # 创建并启动任务线程
        thread = threading.Thread(
            target=self._download_task_thread,
            args=(token, novel_id, novel_name, novel_author, novel_desc, chapter_ids, chapter_titles, format_choice),
            daemon=True
        )
        self.task_threads[novel_id] = thread
        thread.start()

        self.logger.info(f"启动下载任务: {novel_name} (ID: {novel_id})")

        # 返回任务信息
        return {
            "task_id": task_id,
            "novel_id": novel_id,
            "novel_name": novel_name,
            "status": task.status,
            "progress": task.progress
        }

    def _download_task_thread(self, token: str, novel_id: str, novel_name: str,
                             novel_author: str, novel_desc: str, chapter_ids=None, chapter_titles=None, format_choice: str = "txt"):
        """
        下载任务线程

        Args:
            token: 激活码
            novel_id: 小说ID
            novel_name: 小说名称
            novel_author: 小说作者
            novel_desc: 小说描述
            chapter_ids: 章节ID列表
            chapter_titles: 章节标题列表
            format_choice: 用户选择的文件格式 (txt/epub)
        """
        task = self.tasks[novel_id]

        try:
            # 更新任务状态为处理中
            task.update_status(DownloadTaskStatus.PROCESSING, progress=10)
            self._notify_status_change(novel_id)

            # 请求后端下载小说
            result = self.api.request_novel_download(
                token, novel_id, novel_name, novel_author, novel_desc, chapter_ids, chapter_titles, format_choice
            )

            # 检查请求结果
            code = result.get("code", "")
            if code == "200" or code == 200:
                # 获取后端任务ID - 根据Java后端代码，taskId在data字段中
                task_id = result.get("taskId", "")
                if task_id:
                    self.logger.info(f"获取到后端任务ID: {task_id}")
                    task.backend_task_id = task_id

                    # 更新进度
                    task.update_status(DownloadTaskStatus.PROCESSING, progress=20)
                    self._notify_status_change(novel_id)

                    # 轮询查询任务状态
                    self._poll_task_status(task_id, novel_id)
                else:
                    # 兼容旧版接口，如果有直接返回小说内容
                    file_url = result.get("data", {}).get("fileUrl", "")
                    if file_url:
                        task.update_status(
                            DownloadTaskStatus.COMPLETED,
                            progress=100,
                            file_url=file_url
                        )
                        self.logger.info(f"下载任务立即完成: {novel_name} (ID: {novel_id}), 文件URL: {file_url}")
                        self._notify_status_change(novel_id)
                    else:
                        # 没有任务ID也没有文件URL，标记为失败
                        task.update_status(
                            DownloadTaskStatus.FAILED,
                            error_message="未获取到任务ID或文件URL"
                        )
                        self.logger.error(f"下载任务失败: {novel_name} (ID: {novel_id}), 未获取到任务ID或文件URL")
                        self._notify_status_change(novel_id)
            else:
                # 请求失败
                error_msg = result.get("msg", "未知错误")
                task.update_status(
                    DownloadTaskStatus.FAILED,
                    error_message=error_msg
                )
                self.logger.error(f"下载任务失败: {novel_name} (ID: {novel_id}), 错误: {error_msg}")
                self._notify_status_change(novel_id)

        except Exception as e:
            # 处理异常
            task.update_status(
                DownloadTaskStatus.FAILED,
                error_message=str(e)
            )
            self.logger.error(f"下载任务异常: {novel_name} (ID: {novel_id}), 异常: {str(e)}")
            self._notify_status_change(novel_id)

    def _download_file(self, file_url: str, novel_name: str) -> str:
        """
        下载文件到本地

        Args:
            file_url: 文件URL
            novel_name: 小说名称

        Returns:
            本地文件路径
        """
        try:
            # 使用配置中的保存路径，而不是硬编码的用户目录
            download_dir = str(self.config.save_path) if self.config.save_path else os.path.join(os.path.expanduser("~"), "Downloads", "Novels")
            
            # 确保下载目录存在
            os.makedirs(download_dir, exist_ok=True)

            # 替换文件名中的非法字符，防止保存文件时出错
            safe_novel_name = "".join(c if c.isalnum() or c in [' ', '_', '-'] else '_' for c in novel_name)
            
            # 构建本地文件路径
            file_name = f"{safe_novel_name}_{int(time.time())}.txt"
            local_path = os.path.join(download_dir, file_name)

            # 下载文件 - 使用更安全的方式处理网络请求和文件操作
            with requests.get(file_url, stream=True, timeout=30) as response:
                response.raise_for_status()
                
                # 使用二进制模式写入文件
                with open(local_path, 'wb') as f:
                    # 使用更小的块大小以减少内存使用
                    for chunk in response.iter_content(chunk_size=4096):
                        if chunk:
                            f.write(chunk)
                            f.flush()  # 确保数据写入磁盘

            self.logger.info(f"文件已下载到: {local_path}")
            return local_path

        except Exception as e:
            self.logger.error(f"下载文件失败: {str(e)}")
            raise

    def _poll_task_status(self, backend_task_id: str, novel_id: str):
        """
        轮询任务状态

        Args:
            backend_task_id: 后端任务ID
            novel_id: 小说ID
        """
        task = self.tasks[novel_id]
        check_count = 0
        max_checks = self.config.task_max_check_times
        check_interval = self.config.task_check_interval

        # 添加连续错误计数器，允许一定数量的临时性错误
        consecutive_errors = 0
        max_consecutive_errors = 3

        while check_count < max_checks:
            # 检查任务是否被取消
            if task.status == DownloadTaskStatus.CANCELED:
                self.logger.info(f"下载任务已取消: {task.novel_name} (ID: {novel_id})")
                return

            try:
                # 查询任务状态 - 使用新的API，直接传递后端任务ID
                result = self.api.check_download_task(backend_task_id)
                status = result.get("status", "")

                # 重置连续错误计数器，因为本次请求成功
                consecutive_errors = 0

                if status == "completed":
                    # 任务完成
                    file_url = result.get("fileUrl", "")
                    if file_url:
                        try:
                            # 下载文件
                            local_path = self._download_file(file_url, task.novel_name)
                            
                            # 更新状态前先记录日志，防止后续操作出错导致丢失信息
                            self.logger.info(f"下载任务完成: {task.novel_name} (ID: {novel_id}), 本地文件路径: {local_path}")
                            
                            # 使用 try-finally 确保无论如何都会通知状态变更
                            try:
                                task.update_status(
                                    DownloadTaskStatus.COMPLETED,
                                    progress=100,
                                    file_url=local_path  # 更新为本地文件路径
                                )
                            finally:
                                # 确保通知状态变更
                                self._notify_status_change(novel_id)
                                
                        except Exception as e:
                            self.logger.error(f"文件下载失败: {str(e)}")
                            task.update_status(
                                DownloadTaskStatus.FAILED,
                                error_message=f"文件下载失败: {str(e)}"
                            )
                            self._notify_status_change(novel_id)
                    else:
                        task.update_status(
                            DownloadTaskStatus.FAILED,
                            error_message="未获取到文件URL"
                        )
                        self.logger.error(f"下载任务失败: {task.novel_name} (ID: {novel_id}), 未获取到文件URL")
                        self._notify_status_change(novel_id)
                    return

                elif status == "processing":
                    # 任务处理中，更新进度
                    progress = result.get("progress", 0)
                    # 确保进度在合理范围内
                    if progress > 0:
                        task.update_status(
                            DownloadTaskStatus.PROCESSING,
                            progress=progress
                        )
                        self.logger.debug(f"下载任务进度: {task.novel_name} (ID: {novel_id}), 进度: {progress}%")
                        self._notify_status_change(novel_id)

                elif status == "failed":
                    # 任务失败
                    error_msg = result.get("errorMessage", "未知错误")
                    task.update_status(
                        DownloadTaskStatus.FAILED,
                        error_message=error_msg
                    )
                    self.logger.error(f"下载任务失败: {task.novel_name} (ID: {novel_id}), 错误: {error_msg}")
                    self._notify_status_change(novel_id)
                    return

                elif status == "error":
                    # API请求成功但返回错误状态，记录错误但继续轮询
                    consecutive_errors += 1
                    error_msg = result.get("errorMessage", "未知错误")
                    self.logger.warning(f"查询任务状态返回错误: {error_msg}, 连续错误次数: {consecutive_errors}")

                    # 只有连续多次错误才放弃
                    if consecutive_errors >= max_consecutive_errors:
                        self.logger.error(f"连续{consecutive_errors}次查询任务状态失败，将继续尝试")
                        # 不将任务标记为失败，而是更新UI显示遇到临时问题
                        task.update_status(
                            DownloadTaskStatus.PROCESSING,
                            error_message=f"查询状态遇到临时性问题，将继续尝试"
                        )
                        self._notify_status_change(novel_id)
                        # 重置连续错误计数，给予新的尝试机会
                        consecutive_errors = 0

            except Exception as e:
                # 请求异常，增加连续错误计数
                consecutive_errors += 1
                self.logger.warning(f"查询任务状态异常: {str(e)}, 连续错误次数: {consecutive_errors}")

                # 只有连续多次错误才考虑任务可能有问题
                if consecutive_errors >= max_consecutive_errors:
                    self.logger.error(f"连续{consecutive_errors}次查询任务状态异常，但将继续尝试")
                    # 更新UI显示网络问题
                    task.update_status(
                        DownloadTaskStatus.PROCESSING,
                        error_message=f"网络连接问题，将继续尝试"
                    )
                    self._notify_status_change(novel_id)
                    # 增加等待时间，网络可能不稳定
                    time.sleep(check_interval * 2)
                    # 不重置错误计数，保持当前值继续累加

            # 增加检查计数
            check_count += 1

            # 如果后端没有返回明确的进度，则基于已检查次数计算进度
            if task.status == DownloadTaskStatus.PROCESSING and (result.get("progress", 0) <= 0):
                # 将进度控制在20%-90%之间，以给用户更好的反馈
                estimated_progress = 20 + int(70 * check_count / max_checks)
                task.update_status(DownloadTaskStatus.PROCESSING, progress=estimated_progress)
                self._notify_status_change(novel_id)

            # 等待下一次检查
            time.sleep(check_interval)

        # 超过最大检查次数，但不一定是失败，可能是需要更长时间
        # 任务可能仍在后台处理，只是超出了客户端的等待时间
        task.update_status(
            DownloadTaskStatus.PROCESSING,
            progress=95,  # 显示高进度，表示可能快完成了
            error_message="查询超时，但任务可能仍在后台处理中，请稍后在我的小说中查看"
        )
        self.logger.warning(f"下载任务查询超时: {task.novel_name} (ID: {novel_id}), 但任务可能仍在后台处理中")
        self._notify_status_change(novel_id)
    
    def get_task_status(self, novel_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            novel_id: 小说ID
            
        Returns:
            任务状态字典，如果任务不存在则返回None
        """
        if novel_id not in self.tasks:
            return None
        
        task = self.tasks[novel_id]
        return {
            "task_id": task.task_id,
            "novel_id": task.novel_id,
            "novel_name": task.novel_name,
            "status": task.status,
            "progress": task.progress,
            "file_url": task.file_url,
            "error_message": task.error_message,
            "create_time": task.create_time,
            "update_time": task.update_time
        }
    
    def cancel_task(self, novel_id: str) -> bool:
        """
        取消任务
        
        Args:
            novel_id: 小说ID
            
        Returns:
            是否成功取消
        """
        if novel_id not in self.tasks:
            return False
        
        task = self.tasks[novel_id]
        
        # 只有处理中或等待中的任务可以取消
        if task.status in [DownloadTaskStatus.PENDING, DownloadTaskStatus.PROCESSING]:
            task.update_status(DownloadTaskStatus.CANCELED)
            self.logger.info(f"取消下载任务: {task.novel_name} (ID: {novel_id})")
            self._notify_status_change(novel_id)
            return True
        
        return False
    
    def _notify_status_change(self, novel_id: str):
        """
        通知任务状态变更
        
        Args:
            novel_id: 小说ID
        """
        if novel_id in self.task_callbacks and self.task_callbacks[novel_id]:
            task_status = self.get_task_status(novel_id)
            if task_status:
                try:
                    self.task_callbacks[novel_id](task_status)
                except Exception as e:
                    self.logger.error(f"调用任务状态回调函数失败: {str(e)}") 