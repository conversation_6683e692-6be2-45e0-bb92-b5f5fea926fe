<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  async created() {
    // 检查用户激活状态并自动跳转
    try {
      const isActivated = await this.$store.dispatch('user/checkActivation')

      if (isActivated) {
        // 如果已激活且当前在激活页面，自动跳转到主页
        if (this.$route.path === '/activation') {
          this.$router.replace('/fanqie').catch(err => {
            // 忽略重复导航错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('路由跳转失败:', err)
            }
          })
        }
      } else {
        // 如果未激活且不在激活页面，跳转到激活页面
        if (this.$route.path !== '/activation') {
          this.$router.replace('/activation').catch(err => {
            // 忽略重复导航错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('路由跳转失败:', err)
            }
          })
        }
      }
    } catch (error) {
      console.error('检查激活状态失败:', error)
    }
  }
}
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  min-height: 100vh;
}

// Element UI 样式覆盖
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s;

  &--primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8, #6a4190);
      transform: translateY(-1px);
    }
  }

  &--large {
    padding: 12px 24px;
    font-size: 16px;
  }
}

.el-card {
  border-radius: 16px;
  border: none;
  overflow: hidden;

  .el-card__header {
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.el-input {
  .el-input__inner {
    border-radius: 8px;
    border: 1px solid #e0e6ed;
    transition: all 0.3s;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }

  &--large .el-input__inner {
    height: 48px;
    font-size: 16px;
  }
}

.el-progress {
  .el-progress-bar__outer {
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.1);
  }

  .el-progress-bar__inner {
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
  }
}

.el-checkbox {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #667eea;
    border-color: #667eea;
  }
}

.el-radio-button {
  .el-radio-button__inner {
    border-radius: 6px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    transition: all 0.3s;

    &:hover {
      color: #667eea;
      border-color: #667eea;
    }
  }

  &.is-active .el-radio-button__inner {
    background: #667eea;
    border-color: #667eea;
    color: white;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
</style>
