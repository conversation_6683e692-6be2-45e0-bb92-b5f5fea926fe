// 任务管理状态
const state = {
  // 任务列表
  tasks: [],
  
  // 当前选中的任务
  selectedTask: null,
  
  // 任务统计
  taskStats: {
    total: 0,
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  }
}

const mutations = {
  SET_TASKS(state, tasks) {
    state.tasks = tasks
    updateTaskStats(state)
  },
  
  ADD_TASK(state, task) {
    state.tasks.unshift(task)
    updateTaskStats(state)
  },
  
  UPDATE_TASK(state, { taskId, updates }) {
    const index = state.tasks.findIndex(task => task.id === taskId)
    if (index !== -1) {
      state.tasks.splice(index, 1, { ...state.tasks[index], ...updates })
      updateTaskStats(state)
    }
  },
  
  REMOVE_TASK(state, taskId) {
    const index = state.tasks.findIndex(task => task.id === taskId)
    if (index !== -1) {
      state.tasks.splice(index, 1)
      updateTaskStats(state)
    }
  },
  
  SET_SELECTED_TASK(state, task) {
    state.selectedTask = task
  },
  
  CLEAR_COMPLETED_TASKS(state) {
    state.tasks = state.tasks.filter(task => task.status !== 'completed')
    updateTaskStats(state)
  },
  
  CLEAR_ALL_TASKS(state) {
    state.tasks = []
    state.selectedTask = null
    updateTaskStats(state)
  }
}

// 更新任务统计
function updateTaskStats(state) {
  const stats = {
    total: state.tasks.length,
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  }
  
  state.tasks.forEach(task => {
    stats[task.status] = (stats[task.status] || 0) + 1
  })
  
  state.taskStats = stats
}

const actions = {
  // 加载任务列表
  async loadTasks({ commit }) {
    try {
      // 从本地存储加载任务
      const savedTasks = localStorage.getItem('novel_downloader_tasks')
      if (savedTasks) {
        const tasks = JSON.parse(savedTasks)
        commit('SET_TASKS', tasks)
      }
    } catch (error) {
      console.error('加载任务列表失败:', error)
    }
  },
  
  // 保存任务列表
  saveTasks({ state }) {
    try {
      localStorage.setItem('novel_downloader_tasks', JSON.stringify(state.tasks))
    } catch (error) {
      console.error('保存任务列表失败:', error)
    }
  },
  
  // 创建新任务
  createTask({ commit, dispatch }, taskData) {
    const task = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      platform: taskData.platform,
      novelId: taskData.novelId,
      novelName: taskData.novelName,
      novelAuthor: taskData.novelAuthor,
      novelUrl: taskData.novelUrl,
      coverUrl: taskData.coverUrl,
      totalChapters: taskData.totalChapters,
      selectedChapters: taskData.selectedChapters,
      format: taskData.format,
      status: 'pending', // pending, processing, completed, failed
      progress: 0,
      currentChapter: '',
      speed: 0,
      eta: 0,
      fileUrl: '',
      filePath: '',
      fileSize: 0,
      errorMessage: '',
      createdAt: new Date().toISOString(),
      startedAt: null,
      completedAt: null
    }
    
    commit('ADD_TASK', task)
    dispatch('saveTasks')
    
    return task
  },
  
  // 更新任务
  updateTask({ commit, dispatch }, { taskId, updates }) {
    commit('UPDATE_TASK', { taskId, updates })
    dispatch('saveTasks')
  },
  
  // 删除任务
  removeTask({ commit, dispatch }, taskId) {
    commit('REMOVE_TASK', taskId)
    dispatch('saveTasks')
  },
  
  // 开始任务
  async startTask({ commit, dispatch, rootGetters }, taskId) {
    try {
      const task = state.tasks.find(t => t.id === taskId)
      if (!task) return
      
      // 更新任务状态
      const updates = {
        status: 'processing',
        startedAt: new Date().toISOString(),
        progress: 0
      }
      
      commit('UPDATE_TASK', { taskId, updates })
      dispatch('saveTasks')
      
      // 调用下载API
      const downloadData = {
        token: rootGetters['user/activationToken'],
        novelId: task.novelId,
        novelName: task.novelName,
        novelAuthor: task.novelAuthor,
        chapterIds: task.selectedChapters,
        format: task.format
      }
      
      // 这里应该调用实际的API
      // const response = await api.startDownload(downloadData)
      
      // 模拟任务进度更新
      simulateTaskProgress(taskId, commit, dispatch)
      
    } catch (error) {
      const updates = {
        status: 'failed',
        errorMessage: error.message || '任务启动失败'
      }
      commit('UPDATE_TASK', { taskId, updates })
      dispatch('saveTasks')
    }
  },
  
  // 停止任务
  stopTask({ commit, dispatch }, taskId) {
    const updates = {
      status: 'failed',
      errorMessage: '用户取消'
    }
    commit('UPDATE_TASK', { taskId, updates })
    dispatch('saveTasks')
  },
  
  // 重试任务
  retryTask({ dispatch }, taskId) {
    dispatch('startTask', taskId)
  },
  
  // 清除已完成任务
  clearCompletedTasks({ commit, dispatch }) {
    commit('CLEAR_COMPLETED_TASKS')
    dispatch('saveTasks')
  },
  
  // 清除所有任务
  clearAllTasks({ commit, dispatch }) {
    commit('CLEAR_ALL_TASKS')
    dispatch('saveTasks')
  },
  
  // 选择任务
  selectTask({ commit }, task) {
    commit('SET_SELECTED_TASK', task)
  }
}

// 模拟任务进度
function simulateTaskProgress(taskId, commit, dispatch) {
  let progress = 0
  const interval = setInterval(() => {
    progress += Math.random() * 5 + 2
    
    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
      
      const updates = {
        status: 'completed',
        progress: 100,
        completedAt: new Date().toISOString(),
        fileUrl: `http://example.com/download/${taskId}.txt`,
        filePath: `D:/Downloads/Novels/novel_${taskId}.txt`,
        fileSize: Math.floor(Math.random() * 1000000) + 500000
      }
      
      commit('UPDATE_TASK', { taskId, updates })
      dispatch('saveTasks')
    } else {
      const updates = {
        progress: Math.min(progress, 100),
        currentChapter: `第${Math.floor(progress / 10) + 1}章`,
        speed: Math.random() * 100 + 50,
        eta: (100 - progress) * 2
      }
      
      commit('UPDATE_TASK', { taskId, updates })
    }
  }, 1000)
}

const getters = {
  tasks: state => state.tasks,
  selectedTask: state => state.selectedTask,
  taskStats: state => state.taskStats,
  
  // 按状态筛选任务
  pendingTasks: state => state.tasks.filter(task => task.status === 'pending'),
  processingTasks: state => state.tasks.filter(task => task.status === 'processing'),
  completedTasks: state => state.tasks.filter(task => task.status === 'completed'),
  failedTasks: state => state.tasks.filter(task => task.status === 'failed'),
  
  // 获取任务
  getTaskById: state => id => state.tasks.find(task => task.id === id)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
