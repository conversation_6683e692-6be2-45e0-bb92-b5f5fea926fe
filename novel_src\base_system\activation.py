import os
import uuid
import json
import platform
import hashlib
import requests
import subprocess
from pathlib import Path
from typing import Tuple, Optional
from ..constants import API_BASE_URL
from .context import GlobalContext


class ActivationManager:
    """激活码管理器，负责处理激活码的验证和机器码生成"""

    TOKEN_VERIFY_API = API_BASE_URL + "/fq_token/selectByName?tokenName="
    TOKEN_UPDATE_API = API_BASE_URL + "/fq_token/update"
    TOKEN_FILE = "activation_token.json"

    def __init__(self):
        self.logger = GlobalContext.get_logger()
        self.config = GlobalContext.get_config()
        self.network = GlobalContext.get_network_client()
        self.token_path = self._get_token_path()

    def _get_token_path(self) -> Path:
        """获取激活码存储路径"""
        app_data_dir = Path.home() / ".tomato_novel_downloader"
        app_data_dir.mkdir(exist_ok=True)
        return app_data_dir / self.TOKEN_FILE

    def get_machine_code(self) -> str:
        """
        获取当前机器的唯一标识码
        使用多层级稳定硬件标识生成，确保重启和重装系统后保持一致
        """
        try:
            # 获取系统信息
            system = platform.system()

            # 获取稳定的硬件标识符列表
            hardware_identifiers = []

            # 1. 获取主板/系统UUID（最稳定的标识符）
            system_uuid = self._get_system_uuid(system)
            if system_uuid:
                hardware_identifiers.append(f"sys_uuid:{system_uuid}")

            # 2. 获取CPU信息（相对稳定）
            cpu_info = self._get_cpu_identifier(system)
            if cpu_info:
                hardware_identifiers.append(f"cpu:{cpu_info}")

            # 3. 获取主硬盘序列号（较稳定，但需要特殊处理）
            disk_serial = self._get_primary_disk_serial(system)
            if disk_serial:
                hardware_identifiers.append(f"disk:{disk_serial}")

            # 4. 获取网卡MAC地址（作为备用标识）
            mac_address = self._get_stable_mac_address()
            if mac_address:
                hardware_identifiers.append(f"mac:{mac_address}")

            # 5. 添加系统类型作为区分
            hardware_identifiers.append(f"os:{system}")

            # 如果没有获取到任何硬件标识符，使用fallback方法
            if not hardware_identifiers:
                self.logger.warning("无法获取任何硬件标识符，使用fallback方法")
                return self._get_fallback_machine_code()

            # 将所有标识符组合并生成机器码
            combined = "|".join(sorted(hardware_identifiers))  # 排序确保一致性

            # 使用SHA256哈希算法计算机器码
            hash_obj = hashlib.sha256(combined.encode('utf-8'))
            machine_code = hash_obj.hexdigest()

            self.logger.debug(f"机器码生成成功，使用标识符: {len(hardware_identifiers)}个")
            return machine_code

        except Exception as e:
            self.logger.error(f"获取机器码失败: {str(e)}")
            return self._get_fallback_machine_code()

    def _get_system_uuid(self, system: str) -> str:
        """获取系统/主板UUID"""
        try:
            if system == "Windows":
                # 优先获取主板UUID
                result = subprocess.run(
                    ["wmic", "csproduct", "get", "UUID"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                    for line in lines[1:]:  # 跳过标题行
                        if line and line != "UUID" and len(line) > 10:
                            return line.strip()

                # 备用方法：获取BIOS序列号
                result = subprocess.run(
                    ["wmic", "bios", "get", "SerialNumber"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                    for line in lines[1:]:
                        if line and line != "SerialNumber" and len(line) > 3:
                            return f"bios:{line.strip()}"

            elif system == "Linux":
                # 尝试从DMI读取系统UUID
                try:
                    with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                        uuid_str = f.read().strip()
                        if uuid_str and len(uuid_str) > 10:
                            return uuid_str
                except:
                    pass

                # 备用方法：读取主板序列号
                try:
                    with open('/sys/class/dmi/id/board_serial', 'r') as f:
                        serial = f.read().strip()
                        if serial and len(serial) > 3:
                            return f"board:{serial}"
                except:
                    pass

            elif system == "Darwin":  # macOS
                result = subprocess.run(
                    ["system_profiler", "SPHardwareDataType"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=15
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if "Hardware UUID" in line:
                            uuid_str = line.split(':')[1].strip()
                            if uuid_str and len(uuid_str) > 10:
                                return uuid_str
                        elif "Serial Number" in line:
                            serial = line.split(':')[1].strip()
                            if serial and len(serial) > 3:
                                return f"hw_serial:{serial}"

        except Exception as e:
            self.logger.debug(f"获取系统UUID失败: {str(e)}")

        return ""

    def _get_cpu_identifier(self, system: str) -> str:
        """获取CPU标识符"""
        try:
            if system == "Windows":
                result = subprocess.run(
                    ["wmic", "cpu", "get", "ProcessorId"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                    for line in lines[1:]:
                        if line and line != "ProcessorId" and len(line) > 5:
                            return line.strip()

            elif system == "Linux":
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if line.startswith('processor') and ':' in line:
                                # 获取第一个处理器的信息作为标识
                                continue
                            elif 'serial' in line.lower() and ':' in line:
                                serial = line.split(':')[1].strip()
                                if serial and len(serial) > 3:
                                    return serial
                            elif 'model name' in line.lower() and ':' in line:
                                # 使用CPU型号作为备用标识
                                model = line.split(':')[1].strip()
                                if model and len(model) > 5:
                                    return hashlib.md5(model.encode()).hexdigest()[:16]
                except:
                    pass

            elif system == "Darwin":  # macOS
                result = subprocess.run(
                    ["sysctl", "-n", "machdep.cpu.brand_string"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    cpu_brand = result.stdout.strip()
                    if cpu_brand and len(cpu_brand) > 5:
                        return hashlib.md5(cpu_brand.encode()).hexdigest()[:16]

        except Exception as e:
            self.logger.debug(f"获取CPU标识符失败: {str(e)}")

        return ""

    def _get_primary_disk_serial(self, system: str) -> str:
        """获取主硬盘序列号"""
        try:
            if system == "Windows":
                # 获取C盘所在的物理磁盘序列号
                result = subprocess.run(
                    ["wmic", "diskdrive", "where", "DeviceID='\\\\.\\PHYSICALDRIVE0'", "get", "SerialNumber"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                    for line in lines[1:]:
                        if line and line != "SerialNumber" and len(line) > 3:
                            return line.strip()

                # 备用方法：获取卷序列号
                result = subprocess.run(
                    ["vol", "C:"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10,
                    shell=True
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if "序列号" in line or "Serial Number" in line:
                            parts = line.split()
                            if len(parts) > 0:
                                serial = parts[-1].strip()
                                if len(serial) > 3:
                                    return f"vol:{serial}"

            elif system == "Linux":
                # 获取根分区所在磁盘的序列号
                try:
                    result = subprocess.run(
                        ["lsblk", "-o", "NAME,SERIAL", "-n"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if line.strip() and not line.startswith(' '):  # 主设备
                                parts = line.split()
                                if len(parts) >= 2 and parts[1] and len(parts[1]) > 3:
                                    return parts[1]
                except:
                    pass

                # 备用方法：使用设备UUID
                try:
                    result = subprocess.run(
                        ["blkid", "-o", "value", "-s", "UUID", "/dev/sda1"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        uuid_str = result.stdout.strip()
                        if uuid_str and len(uuid_str) > 10:
                            return f"uuid:{uuid_str}"
                except:
                    pass

            elif system == "Darwin":  # macOS
                result = subprocess.run(
                    ["diskutil", "info", "/"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if "Volume UUID" in line:
                            uuid_str = line.split(':')[1].strip()
                            if uuid_str and len(uuid_str) > 10:
                                return uuid_str
                        elif "Disk / Partition UUID" in line:
                            uuid_str = line.split(':')[1].strip()
                            if uuid_str and len(uuid_str) > 10:
                                return f"disk:{uuid_str}"

        except Exception as e:
            self.logger.debug(f"获取磁盘序列号失败: {str(e)}")

        return ""

    def _get_stable_mac_address(self) -> str:
        """获取稳定的MAC地址"""
        try:
            # 获取所有网络接口的MAC地址，选择最稳定的一个
            mac_addresses = []

            # 方法1：使用uuid.getnode()获取主MAC地址
            primary_mac = uuid.getnode()
            if primary_mac and primary_mac != 0x010203040506:  # 避免虚假MAC
                mac_hex = f"{primary_mac:012x}"
                mac_formatted = ":".join([mac_hex[i:i+2] for i in range(0, 12, 2)])
                mac_addresses.append(mac_formatted)

            # 方法2：通过系统命令获取物理网卡MAC
            system = platform.system()
            if system == "Windows":
                try:
                    result = subprocess.run(
                        ["getmac", "/fo", "csv", "/nh"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if line.strip() and ',' in line:
                                mac = line.split(',')[0].strip('"')
                                if mac and len(mac) == 17 and mac.count('-') == 5:
                                    mac_formatted = mac.replace('-', ':').lower()
                                    if mac_formatted not in mac_addresses:
                                        mac_addresses.append(mac_formatted)
                except:
                    pass

            elif system in ["Linux", "Darwin"]:
                try:
                    result = subprocess.run(
                        ["ifconfig"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        import re
                        mac_pattern = r'([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}'
                        matches = re.findall(mac_pattern, result.stdout)
                        for match in matches:
                            mac = match[0] + match[1] + match[2] + match[3] + match[4] + result.stdout[result.stdout.find(match):result.stdout.find(match)+17]
                            if mac and len(mac) == 17:
                                mac_formatted = mac.replace('-', ':').lower()
                                if mac_formatted not in mac_addresses and not mac_formatted.startswith('00:00:00'):
                                    mac_addresses.append(mac_formatted)
                except:
                    pass

            # 选择最稳定的MAC地址（排除虚拟网卡）
            for mac in sorted(mac_addresses):
                # 排除常见的虚拟网卡MAC地址前缀
                if not any(mac.startswith(prefix) for prefix in [
                    '00:00:00', '00:05:69', '00:0c:29', '00:1c:14', '00:50:56',  # VMware
                    '08:00:27',  # VirtualBox
                    '00:15:5d',  # Hyper-V
                    '02:00:4c', '00:16:3e'  # Xen
                ]):
                    return mac

            # 如果没有找到物理网卡，返回第一个可用的MAC
            if mac_addresses:
                return mac_addresses[0]

        except Exception as e:
            self.logger.debug(f"获取MAC地址失败: {str(e)}")

        return ""

    def _get_fallback_machine_code(self) -> str:
        """获取fallback机器码"""
        try:
            # 使用多个fallback标识符
            fallback_identifiers = []

            # 1. MAC地址
            mac = uuid.getnode()
            if mac:
                fallback_identifiers.append(f"mac:{mac}")

            # 2. 用户名
            try:
                import getpass
                username = getpass.getuser()
                if username:
                    fallback_identifiers.append(f"user:{username}")
            except:
                pass

            # 3. 系统信息
            fallback_identifiers.append(f"platform:{platform.platform()}")

            # 4. Python版本（作为环境标识）
            fallback_identifiers.append(f"python:{platform.python_version()}")

            if fallback_identifiers:
                combined = "|".join(sorted(fallback_identifiers))
                return hashlib.md5(combined.encode('utf-8')).hexdigest()
            else:
                # 最后的fallback
                return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()

        except Exception as e:
            self.logger.error(f"Fallback机器码生成失败: {str(e)}")
            return hashlib.md5(b"fallback_machine_code").hexdigest()

    def verify_token(self, token: str) -> Tuple[bool, str]:
        """
        验证激活码
        返回: (是否激活成功, 错误信息或成功信息)
        """
        try:
            # 获取当前机器码
            machine_code = self.get_machine_code()

            # 请求验证API
            url = f"{self.TOKEN_VERIFY_API}{token}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # 解析JSON响应
            result = response.json()

            # 检查返回状态
            if result.get("code") == "500":
                return False, result.get("message", "激活码不存在")
            elif result.get("code") == "520":
                return False, result.get("message", "激活码已禁用")
            elif result.get("code") == "510":
                # 激活码未被使用，需要绑定到当前机器
                update_url = f"{self.TOKEN_UPDATE_API}?tokenName={token}&tokenCompCode={machine_code}"
                update_response = requests.post(update_url, timeout=10)
                update_response.raise_for_status()

                update_res = update_response.json()
                update_flag = update_res.get("code")

                if update_response.status_code == 200:
                    if update_flag == 200:
                        # 保存激活信息到本地
                        self._save_token(token, machine_code)
                        return True, "激活成功"
                    else:
                        return False, f"激活失败：服务异常"
                else:
                    return False, f"激活失败: 网络问题"
            elif result.get("code") == "540":
                # 访问频繁限制
                return False, result.get("message", "访问频繁，请12小时后重试")
            elif "tokenCode" in result:
                # 保存激活码
                self._save_token(token, machine_code)
                return True, "激活成功"
            else:
                return False, "未知响应格式"

        except Exception as e:
            self.logger.error(f"验证激活码失败: {str(e)}")
            return False, f"激活失败，请检查网络连接"

    def _save_token(self, token: str, machine_code: str) -> None:
        """保存激活信息到本地文件"""
        try:
            token_data = {
                "token": token,
                "machine_code": machine_code,
                "activated_at": str(uuid.uuid4())  # 使用UUID作为激活时间戳
            }

            with open(self.token_path, 'w', encoding='utf-8') as f:
                json.dump(token_data, f)

            self.logger.info(f"激活信息已保存到: {self.token_path}")
        except Exception as e:
            self.logger.error(f"保存激活信息失败: {str(e)}")

    def load_saved_token(self) -> Optional[str]:
        """加载已保存的激活码"""
        try:
            if not self.token_path.exists():
                return None

            with open(self.token_path, 'r', encoding='utf-8') as f:
                token_data = json.load(f)

            return token_data.get("token")
        except Exception as e:
            self.logger.error(f"加载激活信息失败: {str(e)}")
            return None

    def is_activated(self) -> bool:
        """检查是否已激活，支持机器码兼容性检查"""
        try:
            if not self.token_path.exists():
                return False

            with open(self.token_path, 'r', encoding='utf-8') as f:
                token_data = json.load(f)

            saved_token = token_data.get("token")
            saved_machine_code = token_data.get("machine_code")

            if not saved_token or not saved_machine_code:
                return False

            # 获取当前机器码
            current_machine_code = self.get_machine_code()

            # 如果机器码完全一致，直接验证激活码
            if saved_machine_code == current_machine_code:
                is_valid, _ = self.verify_token(saved_token)
                return is_valid

            # 机器码不一致时，进行兼容性检查
            self.logger.info("检测到机器码变化，进行兼容性验证...")

            # 检查是否是可接受的机器码变化（如重启、系统更新等）
            if self._is_machine_code_compatible(saved_machine_code, current_machine_code):
                self.logger.info("机器码变化在可接受范围内，更新本地机器码")
                # 更新本地保存的机器码
                self._update_saved_machine_code(saved_token, current_machine_code)
                # 重新验证激活码
                is_valid, _ = self.verify_token(saved_token)
                return is_valid
            else:
                self.logger.warning("机器码变化超出可接受范围，需要重新激活")
                return False

        except Exception as e:
            self.logger.error(f"检查激活状态失败: {str(e)}")
            return False

    def _is_machine_code_compatible(self, old_code: str, new_code: str) -> bool:
        """
        检查新旧机器码是否兼容
        允许某些非关键硬件信息的变化
        """
        try:
            # 如果完全相同，直接返回True
            if old_code == new_code:
                return True

            # 获取用于生成旧机器码的硬件信息（模拟）
            # 由于无法逆向SHA256，我们通过重新生成来比较关键组件

            # 获取当前的硬件标识符
            system = platform.system()
            current_identifiers = []

            # 获取关键硬件标识符
            system_uuid = self._get_system_uuid(system)
            if system_uuid:
                current_identifiers.append(f"sys_uuid:{system_uuid}")

            cpu_info = self._get_cpu_identifier(system)
            if cpu_info:
                current_identifiers.append(f"cpu:{cpu_info}")

            mac_address = self._get_stable_mac_address()
            if mac_address:
                current_identifiers.append(f"mac:{mac_address}")

            # 如果关键硬件标识符都存在，认为是同一台机器
            if len(current_identifiers) >= 2:
                self.logger.debug("检测到足够的关键硬件标识符，认为是兼容的机器码变化")
                return True

            # 如果关键标识符不足，进行更严格的检查
            # 这里可以添加更多的兼容性规则

            return False

        except Exception as e:
            self.logger.error(f"机器码兼容性检查失败: {str(e)}")
            return False

    def _update_saved_machine_code(self, token: str, new_machine_code: str) -> None:
        """更新本地保存的机器码"""
        try:
            if not self.token_path.exists():
                return

            with open(self.token_path, 'r', encoding='utf-8') as f:
                token_data = json.load(f)

            # 更新机器码，保留其他信息
            token_data["machine_code"] = new_machine_code
            token_data["updated_at"] = str(uuid.uuid4())  # 添加更新时间戳

            with open(self.token_path, 'w', encoding='utf-8') as f:
                json.dump(token_data, f, ensure_ascii=False, indent=2)

            self.logger.info("本地机器码已更新")

        except Exception as e:
            self.logger.error(f"更新本地机器码失败: {str(e)}")

    def get_machine_code_info(self) -> dict:
        """
        获取机器码生成的详细信息，用于诊断
        返回包含各个硬件标识符的字典
        """
        try:
            system = platform.system()
            info = {
                "system": system,
                "machine_code": "",
                "identifiers": {},
                "status": "success"
            }

            # 获取各个硬件标识符
            system_uuid = self._get_system_uuid(system)
            if system_uuid:
                info["identifiers"]["system_uuid"] = system_uuid

            cpu_info = self._get_cpu_identifier(system)
            if cpu_info:
                info["identifiers"]["cpu_identifier"] = cpu_info

            disk_serial = self._get_primary_disk_serial(system)
            if disk_serial:
                info["identifiers"]["disk_serial"] = disk_serial

            mac_address = self._get_stable_mac_address()
            if mac_address:
                info["identifiers"]["mac_address"] = mac_address

            # 生成机器码
            info["machine_code"] = self.get_machine_code()

            # 统计信息
            info["identifier_count"] = len(info["identifiers"])
            info["is_stable"] = info["identifier_count"] >= 2

            return info

        except Exception as e:
            return {
                "system": platform.system(),
                "machine_code": "",
                "identifiers": {},
                "status": "error",
                "error": str(e),
                "identifier_count": 0,
                "is_stable": False
            }

    def diagnose_machine_code_stability(self) -> dict:
        """
        诊断机器码稳定性
        连续生成多次机器码，检查是否一致
        """
        try:
            codes = []
            for i in range(3):
                code = self.get_machine_code()
                codes.append(code)
                if i < 2:  # 不在最后一次等待
                    import time
                    time.sleep(0.1)  # 短暂等待

            is_stable = len(set(codes)) == 1  # 所有机器码都相同

            return {
                "is_stable": is_stable,
                "codes": codes,
                "unique_codes": len(set(codes)),
                "machine_code_info": self.get_machine_code_info()
            }

        except Exception as e:
            return {
                "is_stable": False,
                "codes": [],
                "unique_codes": 0,
                "error": str(e),
                "machine_code_info": {}
            }