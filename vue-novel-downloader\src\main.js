import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 自定义样式
import '@/styles/index.scss'

// 工具函数
import * as utils from '@/utils'

// API服务 - 统一管理
import api from '@/api'

// 全局配置
Vue.config.productionTip = false

// 注册Element UI
Vue.use(ElementUI, {
  size: 'medium' // 设置组件默认尺寸
})

// 全局属性
Vue.prototype.$api = api
Vue.prototype.$utils = utils
Vue.prototype.$baseUrl = "http://toolbox.zjzaki.cn/prod-api"

// 全局过滤器
Vue.filter('formatTime', (value) => {
  if (!value) return ''
  const date = new Date(value)
  return date.toLocaleString('zh-CN')
})

Vue.filter('formatFileSize', (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
})

// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  // 可以在这里添加错误上报逻辑
}

// 创建Vue实例
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
