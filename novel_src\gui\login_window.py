"""
login_window.py - 登录激活窗口模块
"""
import os
from typing import Optional, Callable

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QLineEdit, QPushButton, QMessageBox, QWidget,
    QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QIcon

from ..base_system.context import GlobalContext
from ..base_system.activation import ActivationManager
import ctypes


class LoginWindow(QDialog):
    """登录激活窗口"""
    
    # 定义一个信号，用于通知登录成功
    login_success = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)

        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("myappid")
        
        # 初始化组件
        self.logger = GlobalContext.get_logger()
        self.activation_manager = ActivationManager()
        
        # 设置窗口属性
        self.setWindowTitle("Useful_novel_tool - 激活")
        self.setMinimumWidth(400)
        self.setMinimumHeight(250)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
        
        # 初始化UI
        self.init_ui()
        
        # 尝试加载已保存的激活码
        saved_token = self.activation_manager.load_saved_token()
        if saved_token:
            self.token_input.setText(saved_token)
            self.auto_login()
    
    def init_ui(self):
        """初始化UI布局"""
        main_layout = QVBoxLayout()
        
        # 标题部分
        title_layout = QHBoxLayout()
        logo_label = QLabel()
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "img", "log_ico.ico")
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            logo_label.setPixmap(pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            # 设置窗口图标
            self.setWindowIcon(QIcon(logo_path))
        title_label = QLabel("Useful_novel_tool")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        
        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)
        
        # 添加说明文字
        info_label = QLabel("请输入激活码以继续使用该软件")
        info_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(info_label)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 激活码输入框
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("请输入激活码")
        self.token_input.setMinimumHeight(30)
        # 设置回车键按下事件
        self.token_input.returnPressed.connect(self.verify_activation)
        form_layout.addRow("激活码:", self.token_input)
        
        # 隐藏机器码显示
        # 只在后台获取机器码，不显示在界面上
        self.machine_code = self.activation_manager.get_machine_code()
        
        main_layout.addLayout(form_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 激活按钮
        self.activate_button = QPushButton("激活")
        self.activate_button.setMinimumHeight(30)
        self.activate_button.clicked.connect(self.verify_activation)
        
        # 退出按钮
        self.cancel_button = QPushButton("退出")
        self.cancel_button.setMinimumHeight(30)
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(self.activate_button)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
    
    def auto_login(self):
        """尝试自动登录"""
        try:
            if self.activation_manager.is_activated():
                self.logger.info("激活状态有效，自动登录成功")
                self.login_success.emit()
                self.accept()  # 使用accept确保窗口返回Accepted状态
            else:
                self.logger.warning("已保存的激活信息无效，需要重新激活")
        except Exception as e:
            self.logger.error(f"自动登录失败: {str(e)}")
    
    def verify_activation(self):
        """验证激活码"""
        token = self.token_input.text().strip()
        
        if not token:
            QMessageBox.warning(self, "输入错误", "请输入激活码")
            return
        
        # 禁用按钮，避免重复点击
        self.activate_button.setEnabled(False)
        self.activate_button.setText("验证中...")
        
        # 验证激活码
        try:
            success, message = self.activation_manager.verify_token(token)
            
            if success:
                QMessageBox.information(self, "激活成功", "软件已成功激活，感谢您的支持！")
                self.logger.info(f"激活成功: {message}")
                
                # 发出成功信号并关闭窗口
                self.login_success.emit()
                self.accept()  # 使用accept确保窗口返回Accepted状态
            else:
                QMessageBox.warning(self, "激活失败", message)
                self.logger.warning(f"激活失败: {message}")
                # 恢复按钮状态
                self.activate_button.setEnabled(True)
                self.activate_button.setText("激活")
        except Exception as e:
            QMessageBox.critical(self, "激活错误", f"激活过程中发生错误: {str(e)}")
            self.logger.error(f"激活过程中发生错误: {str(e)}")
            # 恢复按钮状态
            self.activate_button.setEnabled(True)
            self.activate_button.setText("激活")
    
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 如果用户未激活就关闭窗口，应该退出程序
        if not self.activation_manager.is_activated():
            reply = QMessageBox.question(
                self, 
                "确认退出", 
                "您尚未激活软件，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept() 