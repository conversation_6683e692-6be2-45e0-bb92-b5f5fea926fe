#!/usr/bin/env python3
"""
快速打包脚本 - 简化版
适用于开发测试阶段的快速打包
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def quick_build():
    """快速构建可执行文件"""
    print("🚀 快速构建模式")
    print("=" * 40)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 请先安装PyInstaller: pip install pyinstaller")
        return False
    
    # 项目信息
    project_root = Path(__file__).parent
    main_file = project_root / "main.py"
    
    if not main_file.exists():
        print(f"❌ 未找到主文件: {main_file}")
        return False
    
    # 应用名称
    app_name = "某茄小说下载器"
    if platform.system() == "Windows":
        app_name += ".exe"
    
    # 图标文件
    icon_file = project_root / "img" / "log_ico.ico"
    icon_option = ["--icon", str(icon_file)] if icon_file.exists() else []
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 单文件模式
        "--windowed",  # 无控制台窗口
        "--name", app_name,
        "--clean",
        "--noconfirm",
        # 添加数据文件
        "--add-data", "img;img" if platform.system() == "Windows" else "img:img",
        "--add-data", "config.yml;." if platform.system() == "Windows" else "config.yml:.",
        # 隐藏导入
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui", 
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "novel_src.book_parser.chapter_proofreader",
        "--hidden-import", "novel_src.gui.main_window",
        # 排除不需要的模块
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        "--exclude-module", "numpy",
        "--exclude-module", "pandas",
    ] + icon_option + [str(main_file)]
    
    print("🔨 开始构建...")
    print(f"命令: {' '.join(cmd[:5])} ... (共{len(cmd)}个参数)")
    
    try:
        # 执行构建
        result = subprocess.run(cmd, check=True)
        
        # 检查输出文件
        dist_dir = project_root / "dist"
        exe_file = dist_dir / app_name
        
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)
            print(f"\n✅ 构建成功！")
            print(f"📁 文件位置: {exe_file}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 测试运行
            print("\n🧪 测试运行...")
            test_cmd = [str(exe_file), "--version"]
            try:
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
                if test_result.returncode == 0:
                    print("✅ 测试运行成功")
                    print(f"版本信息: {test_result.stdout.strip()}")
                else:
                    print("⚠️ 测试运行失败，但文件已生成")
            except subprocess.TimeoutExpired:
                print("⚠️ 测试运行超时，但文件已生成")
            except Exception as e:
                print(f"⚠️ 测试运行出错: {e}")
            
            return True
        else:
            print(f"❌ 未找到输出文件: {exe_file}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败，错误代码: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False


def main():
    """主函数"""
    print("某茄小说下载器 - 快速打包工具")
    print(f"系统: {platform.system()} {platform.machine()}")
    print(f"Python: {sys.version}")
    
    try:
        success = quick_build()
        if success:
            print("\n🎉 快速构建完成！")
            print("💡 提示: 这是开发版本，如需发布版本请使用 build_exe.py")
        else:
            print("\n❌ 快速构建失败")
        
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 构建被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
