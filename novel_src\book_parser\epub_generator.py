from ebooklib import epub
import re

from ..base_system.context import GlobalContext


class EpubGenerator:
    def __init__(self, identifier, title, language="en", author=None, description=None, publisher=None):
        """
        初始化EPUB书籍对象
        :param identifier: 书籍唯一标识符
        :param title: 书籍标题
        :param language: 语言代码（默认'en'）
        :param author: 作者（可选）
        :param publisher: 出版社（可选）
        """
        self.book = epub.EpubBook()
        self.logger = GlobalContext.get_logger()

        # 设置基本元数据
        self.book.set_identifier(identifier)
        self.book.set_title(title)
        self.book.set_language(language)

        # 尝试设置封面，但只在能找到封面文件时才设置
        try:
            cover_path = str(GlobalContext.get_config().default_save_dir / f"{title}.jpg")
            self.logger.debug(f"尝试加载封面: {cover_path}")

            with open(cover_path, 'rb') as cover_file:
                cover_content = cover_file.read()  # 获取二进制内容

            # 只有当封面内容非空时才设置封面
            if cover_content and len(cover_content) > 0:
                self.book.set_cover("cover.jpg", cover_content)
                self.logger.debug("封面设置成功")
            else:
                self.logger.warning("封面文件为空，跳过设置封面")
        except FileNotFoundError:
            self.logger.warning(f"封面文件未找到: {cover_path}，跳过设置封面")
        except Exception as e:
            self.logger.warning(f"读取封面失败: {str(e)}，跳过设置封面")

        # 添加可选元数据
        if author:
            self.book.add_author(author)
        if publisher:
            self.book.add_metadata("DC", "publisher", publisher)
        if description:
            self.book.add_metadata("DC", "description", description)

        style = '''
        @namespace epub "http://www.idpf.org/2007/ops";
        body { font-family: "Noto Serif CJK SC", SimSun, serif; }
        h1 { text-align: center; margin: 1em 0; }
        p { text-indent: 2em; margin: 0.5em 0; }
        '''
        nav_css = epub.EpubItem(
            uid="style_nav",
            file_name="style/nav.css",
            media_type="text/css",
            content=style
        )
        self.book.add_item(nav_css)

        self.chapters = []
        self._file_counter = 1  # 用于生成自动文件名

    def add_chapter(self, title, content, file_name=None):
        """
        添加章节到书籍
        :param title: 章节标题
        :param content: HTML内容（不带<html>标签）
        :param file_name: 自定义文件名（可选）
        """
        # 生成自动文件名（如果未提供）
        if not file_name:
            file_name = f"chap_{self._file_counter:02d}.xhtml"
            self._file_counter += 1

        # 创建章节对象
        chapter = epub.EpubHtml(
            title=title, file_name=file_name, lang=self.book.language
        )
        chapter.content = content

        # 添加到书籍
        self.book.add_item(chapter)
        self.chapters.append(chapter)

    def generate(self, output_path, toc=None):
        """
        生成EPUB文件
        :param output_path: 输出文件路径
        :param toc: 自定义目录结构（可选）
        """
        try:
            # 设置默认目录（如果未提供）
            if not toc:
                self.book.toc = [(epub.Section("目录"), self.chapters)]
            else:
                self.book.toc = toc

            # 添加导航文件
            self.book.add_item(epub.EpubNcx())
            self.book.add_item(epub.EpubNav())

            # 设置书脊（spine）
            self.book.spine = ["nav"] + self.chapters

            # 生成文件
            self.logger.info(f"开始生成EPUB文件: {output_path}")
            epub.write_epub(output_path, self.book)
            self.logger.info(f"EPUB文件生成成功: {output_path}")
        except Exception as e:
            self.logger.error(f"EPUB生成失败: {str(e)}")
            raise

    def add_metadata(self, namespace, name, value):
        """
        添加自定义元数据
        :param namespace: 命名空间（如'DC'）
        :param name: 元数据名称
        :param value: 元数据值
        """
        self.book.add_metadata(namespace, name, value)

    def generate_from_text(self, text_content, title, author, description, output_path):
        """
        从纯文本内容生成EPUB电子书
        :param text_content: 文本内容
        :param title: 书籍标题
        :param author: 作者
        :param description: 描述
        :param output_path: 输出路径
        """
        try:
            # 添加作者和描述
            if author:
                self.book.add_author(author)
            if description:
                self.book.add_metadata("DC", "description", description)

            # 添加CSS样式
            style = '''
            body { 
                font-family: "Microsoft YaHei", "SimSun", sans-serif; 
                line-height: 1.8;
                margin: 2em;
            }
            h1 { 
                font-size: 1.8em; 
                border-bottom: 1px solid #ccc;
                padding-bottom: 0.5em;
                text-align: center;
                margin: 1em 0 1.5em;
            }
            p { 
                text-indent: 2em; 
                margin: 0.5em 0;
                line-height: 1.8;
            }
            .content {
                white-space: pre-wrap;
                margin-top: 1.5em;
            }
            '''
            nav_css = epub.EpubItem(
                uid="style_custom",
                file_name="style/custom.css",
                media_type="text/css",
                content=style
            )
            self.book.add_item(nav_css)

            # 添加简介章节
            intro_html = epub.EpubHtml(title="简介", file_name="intro.xhtml", lang=self.book.language)
            intro_content = f"""
            <h1>简介</h1>
            <div class="content">
                <p>{description}</p>
                <p>作者：{author}</p>
            </div>
            """
            intro_html.content = intro_content
            intro_html.add_item(nav_css)
            self.book.add_item(intro_html)
            self.chapters.append(intro_html)

            # 解析文本内容生成章节
            chapters_data = self._parse_chapters_from_text(text_content)

            # 添加所有章节到电子书
            for i, (chapter_title, chapter_content) in enumerate(chapters_data):
                # 创建章节
                chapter = epub.EpubHtml(
                    title=chapter_title,
                    file_name=f"chap_{i + 1:03d}.xhtml",
                    lang=self.book.language
                )

                # 处理HTML内容
                html_content = f"""
                <h1>{chapter_title}</h1>
                <div class="content">
                    <p>{"</p><p>".join(chapter_content.split('\n'))}</p>
                </div>
                """
                chapter.content = html_content
                chapter.add_item(nav_css)

                # 添加到书籍
                self.book.add_item(chapter)
                self.chapters.append(chapter)

            # 设置目录
            self.book.toc = [(epub.Section("目录"), self.chapters)]

            # 添加导航文件
            self.book.add_item(epub.EpubNcx())
            self.book.add_item(epub.EpubNav())

            # 设置书脊（spine）
            self.book.spine = ["nav"] + self.chapters

            # 生成文件
            self.logger.info(f"开始生成EPUB文件: {output_path}")
            epub.write_epub(output_path, self.book)
            self.logger.info(f"EPUB文件生成成功: {output_path}")

        except Exception as e:
            self.logger.error(f"从文本生成EPUB失败: {str(e)}")
            # 不抛出异常，避免程序崩溃
            return False

        return True

    def _parse_chapters_from_text(self, text_content):
        """
        从文本内容中解析章节
        :param text_content: 文本内容
        :return: 章节列表，每个元素为(标题, 内容)元组
        """
        try:
            chapters = []
            lines = text_content.split('\n')

            # 跳过前面可能包含元数据的行
            start_index = 0
            for i, line in enumerate(lines):
                if "===========" in line or i >= 30:  # 最多检查前30行
                    start_index = i + 1
                    break

            # 使用正则表达式匹配章节标题
            chapter_pattern = re.compile(
                r'^第\s*\d+\s*章|^第[零一二三四五六七八九十百千万]+章|^\d+[\s\.\-、:：]+\S+|^第[零一二三四五六七八九十百千万]+[卷篇部集]')

            current_title = "引言"
            current_content = []
            chapter_started = False

            for line in lines[start_index:]:
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                # 检查是否是章节标题
                is_title = bool(chapter_pattern.match(line)) or (len(line) < 35 and "章" in line)

                if is_title or line.startswith('第') and len(line) < 50:
                    # 保存前一章节
                    if chapter_started and current_content:
                        chapters.append((current_title, "\n".join(current_content)))

                    # 开始新章节
                    current_title = line
                    current_content = []
                    chapter_started = True
                else:
                    current_content.append(line)

            # 添加最后一章
            if current_content:
                chapters.append((current_title, "\n".join(current_content)))

            # 如果没有找到章节，可能是没有明确的章节标记，将整个内容作为一章
            if not chapters:
                self.logger.warning("未检测到明确的章节标记，将整个内容作为单章处理")
                content_text = "\n".join([line.strip() for line in lines[start_index:] if line.strip()])
                # 使用书籍标题作为章节标题
                book_title = self.book.title if hasattr(self.book, 'title') else "正文"
                chapters.append((book_title, content_text))

            return chapters

        except Exception as e:
            self.logger.error(f"解析章节失败: {str(e)}")
            # 返回一个包含全部内容的单一章节
            book_title = self.book.title if hasattr(self.book, 'title') else "正文"
            return [(book_title, text_content)]
