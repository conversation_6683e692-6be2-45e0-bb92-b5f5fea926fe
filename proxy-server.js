/**
 * 代理服务器
 * 用于绕过CORS限制，获取小说网站的HTML内容
 */

const express = require('express')
const cors = require('cors')
const axios = require('axios')

const app = express()
const PORT = 3001

// 中间件
app.use(cors())
app.use(express.json())

// 代理路由 - 获取页面内容
app.post('/proxy/fetch-page', async (req, res) => {
  try {
    const { url, headers = {} } = req.body
    
    if (!url) {
      return res.status(400).json({
        success: false,
        error: '缺少URL参数'
      })
    }

    console.log(`代理请求: ${url}`)

    // 发送请求
    const response = await axios.get(url, {
      headers: {
        'User-Agent': headers['User-Agent'] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...headers
      },
      timeout: 30000,
      maxRedirects: 5
    })

    res.json({
      success: true,
      html: response.data,
      status: response.status,
      headers: response.headers
    })

  } catch (error) {
    console.error('代理请求失败:', error.message)
    
    res.status(500).json({
      success: false,
      error: error.message,
      details: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText
      } : null
    })
  }
})

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    port: PORT
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`代理服务器启动成功，端口: ${PORT}`)
  console.log(`健康检查: http://localhost:${PORT}/health`)
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
})
