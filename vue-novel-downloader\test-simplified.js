/**
 * 测试简化后的功能（移除VIP、免费章节、字数显示）
 */

const axios = require('axios')

const TEST_BOOK_ID = '7416289834324479001'
const PROXY_BASE_URL = 'http://localhost:3001'

// 测试简化后的解析功能
async function testSimplifiedParsing() {
    try {
        console.log('=== 测试简化后的解析功能 ===')
        console.log(`测试书籍ID: ${TEST_BOOK_ID}`)
        
        // 通过代理获取章节列表
        const response = await axios.post(`${PROXY_BASE_URL}/proxy/fetch-api`, {
            url: `https://fanqienovel.com/api/reader/directory/detail?bookId=${TEST_BOOK_ID}`,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': `https://fanqienovel.com/page/${TEST_BOOK_ID}`
            }
        })
        
        if (!response.data.success) {
            throw new Error(response.data.error)
        }
        
        const apiData = response.data.data
        
        if (apiData.code !== 0) {
            throw new Error(`API错误: ${apiData.code} - ${apiData.message}`)
        }
        
        // 解析章节数据（简化版，不包含VIP信息）
        const allItemIds = apiData.data?.allItemIds || []
        const chapterLists = apiData.data?.chapterListWithVolume || []
        
        console.log(`✅ 获取章节列表成功`)
        console.log(`总章节数: ${allItemIds.length}`)
        
        // 构建简化的章节列表
        const chapterInfoMap = {}
        
        // 从卷列表中获取章节标题
        for (const volumeChapters of chapterLists) {
            if (Array.isArray(volumeChapters)) {
                for (const chapter of volumeChapters) {
                    const itemId = chapter.itemId
                    if (itemId) {
                        chapterInfoMap[itemId] = {
                            title: chapter.title || '',
                            volumeName: chapter.volumeName || ''
                        }
                    }
                }
            }
        }
        
        // 构建最终章节列表（简化版）
        const chapters = allItemIds.map((chapterId, index) => {
            const chapterInfo = chapterInfoMap[chapterId] || {}
            return {
                id: chapterId,
                title: chapterInfo.title || `第${index + 1}章`,
                index: index + 1,
                volumeName: chapterInfo.volumeName || ''
            }
        })
        
        console.log(`解析完成，章节数: ${chapters.length}`)
        
        // 显示前几章信息
        console.log('前5章信息:')
        chapters.slice(0, 5).forEach(ch => {
            console.log(`  ${ch.index}. ${ch.title}`)
        })
        
        if (chapters.length > 5) {
            console.log(`  ... 还有 ${chapters.length - 5} 章`)
        }
        
        return {
            success: true,
            totalChapters: chapters.length,
            chapters: chapters
        }
        
    } catch (error) {
        console.error('❌ 简化解析测试失败:', error.message)
        return {
            success: false,
            error: error.message
        }
    }
}

// 测试HTML解析（获取书籍基本信息）
async function testHTMLParsing() {
    try {
        console.log('\n=== 测试HTML解析（书籍基本信息） ===')
        
        const response = await axios.post(`${PROXY_BASE_URL}/proxy/fetch-page`, {
            url: `https://fanqienovel.com/page/${TEST_BOOK_ID}`,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        })
        
        if (!response.data.success) {
            throw new Error(response.data.error)
        }
        
        const html = response.data.html
        console.log(`✅ 获取HTML成功，长度: ${html.length} 字符`)
        
        // 简单的HTML解析
        const titleMatch = html.match(/<h1[^>]*>(.*?)<\/h1>/)
        const title = titleMatch ? titleMatch[1].trim() : '未知书名'
        
        // 解析作者
        const authorMatch = html.match(/class="author-name-text"[^>]*>(.*?)<\/span>/)
        const author = authorMatch ? authorMatch[1].trim() : '未知作者'
        
        // 解析简介
        const descMatch = html.match(/class="page-abstract-content"[^>]*>.*?<p[^>]*>(.*?)<\/p>/s)
        const description = descMatch ? descMatch[1].trim() : '无简介'
        
        console.log(`解析结果:`)
        console.log(`  书名: ${title}`)
        console.log(`  作者: ${author}`)
        console.log(`  简介: ${description.substring(0, 50)}...`)
        
        return {
            success: true,
            bookName: title,
            author: author,
            description: description
        }
        
    } catch (error) {
        console.error('❌ HTML解析测试失败:', error.message)
        return {
            success: false,
            error: error.message
        }
    }
}

// 运行完整测试
async function runCompleteTest() {
    console.log('开始测试简化后的功能...')
    console.log('已移除: VIP章节显示、免费章节显示、预计字数显示')
    
    try {
        // 检查代理服务器
        await axios.get(`${PROXY_BASE_URL}/health`)
        console.log('✅ 代理服务器运行正常')
        
        // 测试章节解析
        const chapterResult = await testSimplifiedParsing()
        
        // 测试HTML解析
        const htmlResult = await testHTMLParsing()
        
        // 汇总结果
        console.log('\n=== 测试结果汇总 ===')
        
        if (chapterResult.success && htmlResult.success) {
            console.log('✅ 所有测试通过')
            console.log(`书籍信息:`)
            console.log(`  书名: ${htmlResult.bookName}`)
            console.log(`  作者: ${htmlResult.author}`)
            console.log(`  总章节: ${chapterResult.totalChapters}`)
            console.log(`  简介: ${htmlResult.description.substring(0, 100)}...`)
            
            console.log('\n🎉 简化功能测试完成！')
            console.log('现在界面将只显示:')
            console.log('- 总章节数')
            console.log('- 作者信息')
            console.log('- 全章节下载模式')
            console.log('不再显示VIP/免费章节区分和字数统计')
            
        } else {
            console.log('❌ 部分测试失败')
            if (!chapterResult.success) {
                console.log(`章节解析失败: ${chapterResult.error}`)
            }
            if (!htmlResult.success) {
                console.log(`HTML解析失败: ${htmlResult.error}`)
            }
        }
        
    } catch (error) {
        console.error('❌ 代理服务器连接失败:', error.message)
        console.log('请确保代理服务器正在运行: node proxy-server.js')
    }
}

// 运行测试
if (require.main === module) {
    runCompleteTest().catch(console.error)
}

module.exports = { testSimplifiedParsing, testHTMLParsing }
